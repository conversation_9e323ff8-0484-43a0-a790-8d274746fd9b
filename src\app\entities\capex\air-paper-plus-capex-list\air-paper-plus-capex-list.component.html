<mat-sidenav-container>
  <mat-sidenav class="detail-sidebar" #sidebarRejectEdit mode="over" position="end" disableClose>
    <reject-capex (closeSidebarEvent)="sidebarRejectClosed($event, sidebarRejectEdit)" *ngIf="isReject" [userId]="userId" [capexId]="capexId" [sidebarParams]="rejectSidebarParams"
      (getAllAirPaperListing)="getAllAirPaperListing()">
    </reject-capex>
  </mat-sidenav>
  <mat-sidenav-content class="detail-sidebar-content">
    <mat-sidenav-container>
      <mat-sidenav class="detail-sidebar" #sidebarShare mode="over" position="end" disableClose>
        <app-share-capex (closeSidebarEvent)="sidebarShareClose($event)" *ngIf="isShare" [sidebarParams]="shareSidebarParams" (getAllAirPaperListing)="getAllAirPaperListing()">
        </app-share-capex>
      </mat-sidenav>
      <mat-sidenav-content class="detail-sidebar-content">
        <kt-portlet>
          <!-- PORTLET LOADING | Binded to TABLE Datasource -->
          <kt-portlet-header [title]="'AP_PP_CAPEX.AP_PP_LIST' | translate" [class]="'kt-portlet__head--lg'" [viewLoading$]="loading$">
            <ng-container ktPortletTools>
              <button class="mr-2 mb-2 fastlane-btn btn" mat-raised-button (click)="createFastlaneCAPEX()"><span class="btn-text ml-0 text-white">{{'COMMON.FASTLANE_CAPEX' |
                  translate}}</span></button>
              <button *ngIf="title !== titleName.VIEWER_C1" mat-raised-button color="primary" class="mb-2 mr-2 btn btn-primary apply-btn" (click)="addCapex()" [disabled]="addDisabled">
                <span><img [src]="addImage" alt="add-new" /></span><span class="btn-text">{{'EQUIPMENT.ADD_BUTTON' | translate}}</span></button>
              <button *ngIf="title === titleName.ADMIN || title === titleName.SUPPORT || title !== titleName.VIEWER_C1" color="primary" class="mb-2 btn btn-primary apply-btn"
                [disabled]="!capexData?.length || isSubmitting || !deleteIds.length" [isSubmitting]="isSubmitting" (click)="exportList()" mat-raised-button><span
                  class="btn-text ml-0">{{'COMMON.EXPORT' | translate}}</span></button>
              <button *ngIf="title === titleName.ADMIN || title !== titleName.VIEWER_C1" color="primary" class="mb-2 btn btn-primary apply-btn"
                [disabled]="!capexData?.length || isSubmittingExportAll || exportAllProgress?.totalElement !== exportAllProgress?.progressCount" [isSubmitting]="isSubmittingExportAll" (click)="exportAll()" mat-raised-button>
                <span class="btn-text ml-0">{{'COMMON.EXPORT_ALL' | translate}}</span>
              </button>
              <button *ngIf="title === titleName.ADMIN || title !== titleName.VIEWER_C1" color="primary" class="mb-2 ml-2 btn btn-primary apply-btn"
                [disabled]="!capexData?.length || isDeleting || !deleteIds.length" [isSubmitting]="isDeleting" (click)="deleteAllCAPEX()" mat-raised-button><span
                  class="btn-text ml-0">{{'COMMON.DELETE_ALL' | translate}}</span></button>
              <ng-container *ngIf="exportAllProgress?.totalElement !== exportAllProgress?.progressCount">
                <span class="export-progress">Export Capex Progress: {{exportAllProgress?.progressCount +'/'+ exportAllProgress?.totalElement}}</span>
              </ng-container>
            </ng-container>
          </kt-portlet-header>
          <!-- end::Header -->
        </kt-portlet>

        <kt-portlet>
          <!-- start::Body (attribute: ktPortletBody) -->
          <kt-portlet-body>
            <div class="kt-form kt-margin-b-20">
              <div class="kt-form__filtration">
                <div class="row align-items-center pb-2">
                  <div class="col-30 ml-4 pl-0">
                    <input (keyup.enter)="getAllAirPaperListing(true)" class="form-control" name="customerInput" [formControl]="customerInput" [placeholder]="'DASHBOARD.FILTER.SEARCH_CUSTOMER' | translate" />
                  </div>
                  <div class="col-25"><label class="label-field">{{'DASHBOARD.FILTER.STATUS' | translate}}</label></div>
                  <div class="col-75">
                    <mat-form-field appearance="outline" class="mat-form-field-fluid">
                      <mat-select (keyup.enter)="getAllAirPaperListing(true)" class="form-control" name="status" [formControl]="approvalStatus" [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                        <mat-option *ngFor="let status of capexStatus" [value]="status?.id">{{ status?.value | replaceSymbol}}</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                  <div class="col-25"><label class="label-field">{{'DASHBOARD.FILTER.DATE' | translate}}</label></div>
                  <div class="col-75 mt-1 ml-3">
                    <mat-form-field appearance="fill" class="mat-form-field-fluid">
                      <mat-date-range-input [rangePicker]="rangePicker" (click)="rangePicker.open()">
                        <input matStartDate placeholder="Start date" [formControl]="startDate">
                        <input matEndDate placeholder="End date" [formControl]="endDate">
                      </mat-date-range-input>
                      <mat-datepicker-toggle matSuffix [for]="rangePicker"></mat-datepicker-toggle>
                      <mat-date-range-picker #rangePicker>
                      </mat-date-range-picker>
                    </mat-form-field>
                  </div>
                </div>
              </div>
              <div class="row align-items-center mb-3">
                <ng-container *ngIf="title !== titleName.TM && title !== titleName.FPS">
                  <div class="col-20"><label class="label-field">{{'DASHBOARD.FILTER.TM' | translate}}</label></div>
                  <div class="col-70">
                    <mat-form-field appearance="outline" class="mat-form-field-fluid">
                      <mat-select (keyup.enter)="getAllAirPaperListing(true)" [formControl]="storopackTM" class="form-control select-field-box" [multiple]="false"
                        [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                        <mat-option>
                          <ngx-mat-select-search [placeholderLabel]="'COMMON.SEARCH' | translate" [noEntriesFoundLabel]="'COMMON.NO_DATA' | translate"
                            [formControl]="tmSearchField">
                          </ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let sale of salesPersonByTitle" [value]="sale?.id">{{sale?.name}}</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </ng-container>
                <ng-container *ngIf="title === titleName.ADMIN || title === titleName.SUPPORT || title === titleName.ISS_NATIONAL">
                  <div class="col-20"><label class="label-field">{{'AP_PP_CAPEX.LABEL.NA_NAME' | translate}}</label></div>
                  <div class="col-70">
                    <mat-form-field appearance="outline" class="mat-form-field-fluid">
                      <mat-select (keyup.enter)="getAllAirPaperListing(true)" [formControl]="nam" class="form-control select-field-box" [multiple]="false"
                        [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                        <mat-option>
                          <ngx-mat-select-search [placeholderLabel]="'COMMON.SEARCH' | translate" [noEntriesFoundLabel]="'COMMON.NO_DATA' | translate"
                            [formControl]="namSearchField">
                          </ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let nam of nationalAccountList" [value]="nam?.id">{{nam?.name}}</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </ng-container>
                <div class="col-15">
                  <mat-checkbox class="archive-class" [formControl]="showArchive" (change)="handleArchived($event)">{{'COMMON.SHOW_ARCHIVE' | translate}}
                  </mat-checkbox>
                </div>
                <div class="col-15" *ngIf="title === titleName.ADMIN">
                  <mat-checkbox class="archive-class" [formControl]="showDeleted" (change)="handleDeleted($event)">{{'COMMON.SHOW_DELETED' | translate}}
                  </mat-checkbox>
                </div>
                <div class="btn-section">
                  <button class="btn btn-primary apply-btn" type="submit" (click)="getAllAirPaperListing(true)" mat-raised-button>{{'NON_EQUIPMENT.FILTER.APPLY_BTN' |
                    translate}}</button>
                  <button class="btn btn-secondary reset-btn" (click)="resetFilter()" mat-raised-button>{{'NON_EQUIPMENT.FILTER.RESET_BTN' | translate}}</button>
                </div>
              </div>
              <!-- start::FILTERS -->
            </div>
            <div class="table-responsive dashboard-table text-nowrap">
              <table class="table table-striped table-no-borders user-table dt-responsive nowrap" id="quote-list" aria-describedby="quote-table">
                <thead class="tableHeader">
                  <tr sflSort [(predicate)]="pageableQuery.orderBy" [(ascending)]="activeReverse" [callback]="getSorting.bind(this)">
                    <th class="text-left" *ngIf="title === titleName.ADMIN">
                      <mat-checkbox name="deleteIds" (change)="deleteAll()" [checked]="deleteAllFlag"></mat-checkbox>
                    </th>
                    <th class="text-left date-width" sflSortBy="id" id="capexId">{{'DASHBOARD.LABEL.ID' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left date-width" sflSortBy="capexDate" id="deviationDate">{{'DASHBOARD.LABEL.CAPEX_DATE' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                      </fa-icon>
                    </th>
                    <th *ngIf="title !== titleName.TM && title !== titleName.FPS" class="text-left action-width" id="tm" sflSortBy="salesPerson">{{'DASHBOARD.FILTER.TM' |
                      translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left action-width" sflSortBy="customer" id="customer">{{'DASHBOARD.LABEL.CUSTOMER' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left approver-id" sflSortBy="distributor" id="distributor">{{'DASHBOARD.LABEL.DISTRIBUTOR' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                      </fa-icon>
                    </th>
                    <th class="text-left" sflSortBy="totalMarginPercent" id="margin">{{'DASHBOARD.LABEL.MARGIN' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left" sflSortBy="color" id="rag">{{'DASHBOARD.LABEL.RAG' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-right margin-width" sflSortBy="totalAmount" id="amount">{{'DASHBOARD.LABEL.AMOUNT' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left" sflSortBy="approver" id="approver">{{'DASHBOARD.LABEL.APPROVER' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left date-width" sflSortBy="approvalStatus" id="status">{{'DASHBOARD.LABEL.STATUS' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                    </th>
                    <th class="text-left date-width" sflSortBy="lastModifiedDate" id="modifiedDate">{{'DASHBOARD.LABEL.MODIFIED_ON' | translate}}<fa-icon [icon]="'sort'"
                        class="ml-1">
                      </fa-icon>
                    </th>
                    <th class="text-left action-width" id="action" data-priority="1" *ngIf="title !== titleName.VIEWER_C1">{{'DASHBOARD.LABEL.ACTION' | translate}}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of capexData; let i = index">
                    <td class="text-left" *ngIf="title === titleName.ADMIN">
                      <mat-checkbox class="mr-3" name="deleteCapex" [(ngModel)]="deleted[i]" value={{data?.id}} (change)="deleteCapexId($event.source.value)">
                      </mat-checkbox>
                    </td>
                    <td class="text-left date-width text-weight"><a class="unique-link" (click)="editCapex(data?.id)">{{ data?.id | number:'4.0' | removeComma }}</a>
                    </td>
                    <td class="text-left date-width text-weight">{{ data?.capexDate | date: "MM/dd/yyyy" }}</td>
                    <td *ngIf="title !== titleName.TM && title !== titleName.FPS" class="text-left text-weight action-width">{{ data?.salesPerson }}</td>
                    <td class="text-left text-weight action-width">{{ data?.customer }}</td>
                    <td class="text-left text-weight approver-id">{{ data?.distributor }}</td>
                    <td class="text-left text-weight">{{ data?.totalMarginPercent }}%</td>
                    <td class="text-left text-weight pl-0"><span class="badge-table-text" [ngClass]="data?.color === 'YELLOW' ? 'badge badge-secondary' : data?.color === 'GREEN' ?
                  'badge badge-primary' : 'badge badge-danger'">{{ data?.color | uppercase }}</span></td>
                    <td class="text-right margin-width text-weight">{{ data?.totalAmount | currency: data?.currency === 'CAD' ? appConstants.canadaCurrency : appConstants.currency
                      }}
                    </td>
                    <td class="text-left date-width text-weight">{{ data?.approver | titlecase }}<span class="approver-title" *ngIf="data?.approverTitle">{{data?.approverTitle ===
                        'VP (Sales)' ? 'VP' : data?.approverTitle}}</span></td>
                    <td class="text-left text-weight date-width"><span class="status-data">{{ data?.approvalStatus | replaceSymbol | uppercase }}</span></td>
                    <td class="text-left date-width text-weight">{{ capexLastModifiedDate[i] }}</td>
                    <td class="text-left action-width text-weight" *ngIf="title !== titleName.VIEWER_C1">
                      <div>
                        <button mat-icon-button color="primary" *ngIf="title !== titleName.VIEWER_C1" (click)="editCapex(data?.id)">
                          <em class="far fa-edit icon-size" [title]="'COMMON.EDIT_CAPEX' | translate"></em>
                        </button>&nbsp;
                        <button mat-icon-button class="ml-2" *ngIf="title !== titleName.VIEWER_C1" color="primary" (click)="cloneCAPEX(data?.id)">
                          <em class="far fa-copy copy-size" [title]="'COMMON.CLONE_CAPEX' | translate"></em>
                        </button>&nbsp;
                        <button *ngIf="(title !== titleName.ADMIN || title !== titleName.VIEWER_C1) && title !== null" mat-icon-button class="ml-2" color="primary" (click)="deleteCAPEX(data?.id)">
                          <em class="flaticon-delete copy-size" [title]="'COMMON.DELETE_CAPEX' | translate"></em>
                        </button>&nbsp;
                        <button mat-icon-button class="ml-2" *ngIf="shareAccess && title !== titleName.VIEWER_C1" color="primary" (click)="openShareSidebar({template: sidebarShare, data: data})">
                          <em class="far fa-share-square copy-size" [title]="'DASHBOARD.SHARE_CAPEX.TITLE' | translate"></em>
                        </button>
                        <button mat-icon-button class="ml-2" *ngIf="title !== titleName.VIEWER_C1" (click)="redirectNewTab(data?.id)" color="primary">
                          <em class="fas fa-external-link-alt icon-size" [title]="'DASHBOARD.OPEN_TAB' | translate"></em>
                        </button>
                        <button mat-icon-button class="ml-2" [matMenuTriggerFor]="menu" [disabled]="data?.approvalStatus === 'OPEN' || disableIcon[i]">
                          <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">
                          <button mat-menu-item *ngIf="isApproveOrReject[i]" (click)="approveOrReject(i, data?.id)">
                            <span>{{'COMMON.APPROVE' | translate}}</span>
                          </button>
                          <button mat-menu-item
                            *ngIf="isApproveOrReject[i] || ((title === titleName.ADMIN || title === titleName.SUPPORT) && data?.approvalStatus === status.PENDING_CS)"
                            (click)="openRejectSidebar($event, sidebarRejectEdit, data?.id)">
                            <span>{{'COMMON.REJECT' | translate}}</span>
                          </button>
                          <button mat-menu-item *ngIf="isSendToCS[i]" (click)="sendCS(i, data?.id)">
                            <span>{{'COMMON.SEND_CS' | translate}}</span>
                          </button>
                          <button mat-menu-item *ngIf="isArchived[i]" (click)="closeCAPEX(i, data?.id)">
                            <span>{{'COMMON.ARCHIVE' | translate}}</span>
                          </button>
                        </mat-menu>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="col-12" *ngIf="!capexData?.length">
                <div class="row">
                  <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
                    {{ 'DASHBOARD.NO_DATA' | translate }}
                  </h6>
                </div>
              </div>
              <div class="col-12" *ngIf="totalElements">
                <div class="d-flex justify-content-between p-2">
                  <ngb-pagination [collectionSize]="totalElements" [boundaryLinks]="true" [maxSize]="5" [(page)]="viewPageableObj.page" [pageSize]="viewPageableObj.size"
                    (pageChange)="getPagination($event, true)">
                    <ng-template ngbPaginationFirst><em class="fa fa-angle-double-left"></em></ng-template>
                    <ng-template ngbPaginationLast><em class="fa fa-angle-double-right"></em></ng-template>
                    <ng-template ngbPaginationPrevious><em class="fa fa-angle-left"></em></ng-template>
                    <ng-template ngbPaginationNext><em class="fa fa-angle-right"></em></ng-template>
                  </ngb-pagination>
                  <div class="d-flex justify-content-between">
                    <label class="mt-2 mr-3">{{'COMMON.ROWS_PER_PAGE' | translate}}</label>
                    <select class="custom-select" style="width: auto" [(ngModel)]="viewPageableObj.size" (ngModelChange)="getPagination($event, false)">
                      <option [ngValue]="pageNumber" *ngFor="let pageNumber of paginationConfig.pageSizeOptions">{{pageNumber}}</option>
                    </select>
                    <app-show-pagination-elements [pageableObject]="totalPaginationObj"></app-show-pagination-elements>
                  </div>
                </div>
              </div>
            </div>
          </kt-portlet-body>
        </kt-portlet>
        <div class="row">
          <div class="col-xl-12">
            <kt-portlet [class]="'mt-5 kt-portlet--height-fluid-half kt-portlet--border-bottom-brand'">
              <kt-portlet-body [class]="'kt-portlet__body--fluid'">
                <div class="row ml-1"><span class="quote-title">{{'IPA_DASHBOARD.TITLE' | translate}}</span></div>
                <!-- start::FILTERS & GROUP ACTIONS -->
                <div class="kt-form kt-margin-b-20">
                  <div class="kt-form__filtration">
                    <div class="row align-items-center pb-2">
                      <div class="col-30 ml-1 pl-0 mb-3">
                        <input (keyup.enter)="getAllIPA(true)" class="form-control" [formControl]="shipNameInput" name="shipNameInput" [placeholder]="'DASHBOARD.FILTER.SEARCH_SHIP_NAME' | translate" />
                      </div>
                      <div class="col-30 ml-5 mb-3">
                        <input (keyup.enter)="getAllIPA(true)" class="form-control" [formControl]="projectInput" name="projectInput" [placeholder]="'DASHBOARD.FILTER.SEARCH_PROJECT' | translate" />
                      </div>
                      <div class="ipa-25"><label class="label-field ml-5">{{'DASHBOARD.FILTER.STATUS' | translate}}</label></div>
                      <div class="col-70">
                        <mat-form-field appearance="outline" class="mat-form-field-fluid">
                          <mat-select (keyup.enter)="getAllIPA(true)" class="form-control" name="status" [formControl]="ipaStatus" [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                            <mat-option *ngFor="let status of statusList" [value]="status">{{ status | replaceSymbol }}</mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="ipa-30"><label class="label-field">{{'IPA_DASHBOARD.LABEL.REQUEST_DATE' | translate}}</label></div>
                      <div class="col-75 mt-1 ml-3">
                        <mat-form-field appearance="fill" class="mat-form-field-fluid">
                          <input matInput [matDatepicker]="dp3" disabled [formControl]="requestDate">
                          <mat-datepicker-toggle matSuffix [for]="dp3"></mat-datepicker-toggle>
                          <mat-datepicker #dp3 disabled="false"></mat-datepicker>
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="row align-items-center mb-3">
                      <ng-container *ngIf="title && title === titleName.RSM">
                        <div class="col-25"><label class="label-field">{{'DASHBOARD.FILTER.TM' | translate}}</label></div>
                        <div class="col-75">
                          <mat-form-field appearance="outline" class="mat-form-field-fluid">
                            <mat-select (keyup.enter)="getAllIPA(true)" [formControl]="ipaStoropackTM" class="form-control select-field-box" [multiple]="false" [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                              <mat-option>
                                <ngx-mat-select-search [placeholderLabel]="'COMMON.SEARCH' | translate" [noEntriesFoundLabel]="'COMMON.NO_DATA' | translate" [formControl]="ipaTMSearchField">
                                </ngx-mat-select-search>
                              </mat-option>
                              <mat-option *ngFor="let sale of salesPersonByRSMTitle" [value]="sale?.name">{{sale?.name}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                      </ng-container>
                      <div class="btn-section">
                        <button type="submit" class="btn btn-primary apply-btn" mat-raised-button (click)="getAllIPA(true)">{{'NON_EQUIPMENT.FILTER.APPLY_BTN' | translate}}</button>
                        <button class="btn btn-secondary reset-btn" mat-raised-button (click)="resetIPAFilter()">{{'NON_EQUIPMENT.FILTER.RESET_BTN' | translate}}</button>
                      </div>
                    </div>
                  </div>
                  <!-- start::FILTERS -->
                </div>
                <div class="table-responsive dashboard-table text-nowrap">
                  <table class="table table-striped table-no-borders user-table dt-responsive nowrap" id="quote-list" aria-describedby="quote-table">
                    <thead class="tableHeader">
                      <tr sflSort [(predicate)]="pageableIPAQuery.orderBy" [(ascending)]="activeReverse" [callback]="getIPASorting.bind(this)">
                        <th sflSortBy="ipaId" class="text-left approver-id" id="capexId">{{'IPA_DASHBOARD.LABEL.ID' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left approver-id" sflSortBy="version" id="modifiedOn">{{'IPA_DASHBOARD.LABEL.VERSION' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" id="date" sflSortBy="shipToName">{{'IPA_DASHBOARD.LABEL.SHIP_TO' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="requestDate" id="deviationDate">{{'IPA_DASHBOARD.LABEL.REQUEST_DATE' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="projectNumber" id="projectNumber">{{'IPA.COMMENT_QUOTE.LABEL.PROJECT_NUMBER' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="createdBy" id="distributor">{{'IPA_DASHBOARD.LABEL.CREATED_BY' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="createdOn" id="createdOn">{{'IPA_DASHBOARD.LABEL.CREATED' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="ipaStatus" id="status">{{'IPA_DASHBOARD.LABEL.STATUS' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left action-width" id="action" data-priority="1" *ngIf="title !== titleName.VIEWER_C1">{{'DASHBOARD.LABEL.ACTION' | translate}}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let data of ipaList; let i = index">
                        <td class="text-left approver-id text-weight"><a class="unique-link"
                            (click)="editIPA(data?.capexId, i, data?.ipaId)">{{ data?.ipaId | number:'4.0' | removeComma }}</a>
                        </td>
                        <td class="text-left text-weight approver-id">{{ data?.version }}</td>
                        <td class="text-left text-weight">{{ data?.shipToName }}</td>
                        <td class="text-left text-weight">{{ data?.requestDate | date: "MM/dd/yyyy" }}</td>
                        <td class="text-left text-weight">{{ data?.projectNumber }}</td>
                        <td class="text-left text-weight">{{ data?.createdBy }}</td>
                        <td class="text-left text-weight">{{ data?.createdOn | date: "MM/dd/yyyy" }}</td>
                        <td class="text-left text-weight"><span class="status-data">{{ data?.ipaStatus | replaceSymbol | uppercase }}</span></td>
                        <td class="text-left text-weight action-width" *ngIf="title !== titleName.VIEWER_C1">
                          <div>
                            <em class="far fa-edit icon-size" [title]="'COMMON.EDIT_IPA' | translate" (click)="editIPA(data?.capexId, i, data?.ipaId)"></em>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="col-12" *ngIf="!ipaList?.length">
                    <div class="row">
                      <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
                        {{ 'IPA_DASHBOARD.NO_IPA' | translate }}
                      </h6>
                    </div>
                  </div>
                  <div class="col-12" *ngIf="totalIPAElements">
                    <div class="d-flex justify-content-between p-2">
                      <ngb-pagination [collectionSize]="totalIPAElements" [boundaryLinks]="true" [maxSize]="5" [(page)]="viewPageableIPAObj.page" [pageSize]="viewPageableIPAObj.size"
                        (pageChange)="getIPAPagination($event, true)">
                        <ng-template ngbPaginationFirst><em class="fa fa-angle-double-left"></em></ng-template>
                        <ng-template ngbPaginationLast><em class="fa fa-angle-double-right"></em></ng-template>
                        <ng-template ngbPaginationPrevious><em class="fa fa-angle-left"></em></ng-template>
                        <ng-template ngbPaginationNext><em class="fa fa-angle-right"></em></ng-template>
                      </ngb-pagination>
                      <div class="d-flex justify-content-between">
                        <label class="mt-2 mr-3">{{'COMMON.ROWS_PER_PAGE' | translate}}</label>
                        <select class="custom-select" style="width: auto" [(ngModel)]="viewPageableIPAObj.size" (ngModelChange)="getIPAPagination($event, false)">
                          <option [ngValue]="pageNumber" *ngFor="let pageNumber of paginationConfig.pageSizeOptions">{{pageNumber}}</option>
                        </select>
                        <app-show-pagination-elements [pageableObject]="totalPaginationIPAObj"></app-show-pagination-elements>
                      </div>
                    </div>
                  </div>
                </div>
              </kt-portlet-body>
            </kt-portlet>
          </div>
        </div>
        <div class="row">
          <div class="col-xl-12">
            <kt-portlet [class]="'mt-5 kt-portlet--height-fluid-half kt-portlet--border-bottom-brand'">
              <kt-portlet-body [class]="'kt-portlet__body--fluid'">
                <div class="row ml-1"><span class="quote-title">{{'IPA_DASHBOARD.CPA_TITLE' | translate}}</span></div>
                <div class="kt-form kt-margin-b-20">
                  <div class="kt-form__filtration">
                    <div class="row align-items-center pb-2">
                      <div class="col-30 ml-4 pl-0">
                        <input (keyup.enter)="getAllCPA(true)" class="form-control" name="cpaCustomerInput" [formControl]="cpaCustomerInput" [placeholder]="'DASHBOARD.FILTER.SEARCH_CUSTOMER' | translate" />
                      </div>
                      <div class="col-25"><label class="label-field">{{'DASHBOARD.FILTER.STATUS' | translate}}</label></div>
                      <div class="col-75">
                        <mat-form-field appearance="outline" class="mat-form-field-fluid">
                          <mat-select (keyup.enter)="getAllCPA(true)" class="form-control" name="cpaApprovalStatus" [formControl]="cpaApprovalStatus" [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                            <mat-option *ngFor="let status of cpaStatus" [value]="status?.id">{{ status?.value | replaceSymbol}}</mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-25"><label class="label-field">{{'DASHBOARD.FILTER.DATE' | translate}}</label></div>
                      <div class="col-75 mt-1 ml-3">
                        <mat-form-field appearance="fill" class="mat-form-field-fluid">
                          <mat-date-range-input [rangePicker]="cpaRangePicker" (click)="cpaRangePicker.open()">
                            <input matStartDate placeholder="Start date" [formControl]="cpaStartDate">
                            <input matEndDate placeholder="End date" [formControl]="cpaEndDate">
                          </mat-date-range-input>
                          <mat-datepicker-toggle matSuffix [for]="cpaRangePicker"></mat-datepicker-toggle>
                          <mat-date-range-picker #cpaRangePicker>
                          </mat-date-range-picker>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                  <div class="row align-items-center mb-3">
                    <ng-container *ngIf="title !== titleName.TM && title !== titleName.FPS">
                      <div class="col-20"><label class="label-field">{{'DASHBOARD.FILTER.TM' | translate}}</label></div>
                      <div class="col-70">
                        <mat-form-field appearance="outline" class="mat-form-field-fluid">
                          <mat-select (keyup.enter)="getAllCPA(true)" [formControl]="cpaStoropackTM" class="form-control select-field-box" [multiple]="false"
                            [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                            <mat-option>
                              <ngx-mat-select-search [placeholderLabel]="'COMMON.SEARCH' | translate" [noEntriesFoundLabel]="'COMMON.NO_DATA' | translate"
                                [formControl]="cpaTMSearchField">
                              </ngx-mat-select-search>
                            </mat-option>
                            <mat-option *ngFor="let sale of salesPersonByCPATitle " [value]="sale?.id">{{sale?.name}}</mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="title === titleName.ADMIN || title === titleName.SUPPORT || title === titleName.ISS_NATIONAL">
                      <div class="col-20"><label class="label-field">{{'AP_PP_CAPEX.LABEL.NA_NAME' | translate}}</label></div>
                      <div class="col-70">
                        <mat-form-field appearance="outline" class="mat-form-field-fluid">
                          <mat-select (keyup.enter)="getAllCPA(true)" [formControl]="cpaNam" class="form-control select-field-box" [multiple]="false"
                            [placeholder]="'NON_EQUIPMENT.FILTER.SELECT_PLACEHOLDER' | translate">
                            <mat-option>
                              <ngx-mat-select-search [placeholderLabel]="'COMMON.SEARCH' | translate" [noEntriesFoundLabel]="'COMMON.NO_DATA' | translate"
                                [formControl]="cpaNamSearchField">
                              </ngx-mat-select-search>
                            </mat-option>
                            <mat-option *ngFor="let nam of cpaNationalAccountList" [value]="nam?.id">{{nam?.name}}</mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </ng-container>
                    <div class="col-15">
                      <mat-checkbox class="archive-class" [formControl]="cpaShowArchive" (change)="handleCPAArchived($event)">{{'COMMON.SHOW_ARCHIVE' | translate}}
                      </mat-checkbox>
                    </div>
                    <div class="col-15" *ngIf="title === titleName.ADMIN">
                      <mat-checkbox class="archive-class" [formControl]="cpaShowDeleted" (change)="handleCPADeleted($event)">{{'COMMON.SHOW_DELETED' | translate}}
                      </mat-checkbox>
                    </div>
                    <div class="btn-section">
                      <button class="btn btn-primary apply-btn" type="submit" (click)="getAllCPA(true)" mat-raised-button>{{'NON_EQUIPMENT.FILTER.APPLY_BTN' |
                        translate}}</button>
                      <button class="btn btn-secondary reset-btn" (click)="resetCPAFilter()" mat-raised-button>{{'NON_EQUIPMENT.FILTER.RESET_BTN' | translate}}</button>
                    </div>
                  </div>
                  <!-- start::FILTERS -->
                </div>
                <div class="table-responsive dashboard-table text-nowrap">
                  <table class="table table-striped table-no-borders user-table dt-responsive" id="quote-list" aria-describedby="quote-table">
                    <thead class="tableHeader">
                      <tr sflSort [(predicate)]="pageableCPAQuery.orderBy" [(ascending)]="activeReverse" [callback]="getCPASorting.bind(this)">
                        <th sflSortBy="capexId" class="text-left" id="capexId">{{'DASHBOARD.LABEL.ID' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th sflSortBy="capexType" class="text-left" id="type">{{'DASHBOARD.LABEL.TYPE' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="version" id="version">{{'AP_PP_CPA.VERSION.VERSION' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                        </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="createdBy" id="creatorTitle">{{'DASHBOARD.LABEL.CREATOR' | translate}}
                        </th>
                        <th class="text-left" sflSortBy="capexDate" id="deviationDate">{{'DASHBOARD.LABEL.CAPEX_DATE' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="cpaType" id="cpatype">{{'AP_PP_CPA.LABEL.CPA_TYPE' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                        </fa-icon>
                        </th>
                        <th *ngIf="title !== titleName.TM && title !== titleName.FPS" class="text-left action-width" id="tm" sflSortBy="territoryManager">{{'DASHBOARD.FILTER.TM' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" id="shipToName" sflSortBy="shipToName">{{'DASHBOARD.LABEL.CUSTOMER' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="soldToName" id="soldToName">{{'DASHBOARD.LABEL.DISTRIBUTOR' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="totalMarginPercent" id="margin">{{'DASHBOARD.LABEL.MARGIN' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="color" id="rag">{{'DASHBOARD.LABEL.RAG' | translate}}<fa-icon [icon]="'sort'" class="ml-1"></fa-icon>
                        </th>
                        <th class="text-right" sflSortBy="totalMarginAmount" id="amount">{{'DASHBOARD.LABEL.AMOUNT' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>

                        <th class="text-left" sflSortBy="approvalStatus" id="status">{{'DASHBOARD.LABEL.STATUS' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" sflSortBy="installationDate" id="modifiedOn">{{'DASHBOARD.LABEL.MODIFIED_ON' | translate}}<fa-icon [icon]="'sort'" class="ml-1">
                          </fa-icon>
                        </th>
                        <th class="text-left" id="action" data-priority="1" *ngIf="title !== titleName.VIEWER_C1">{{'DASHBOARD.LABEL.ACTION' | translate}}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let data of cpaData; let i = index">
                        <td class="text-left text-weight"><a class="unique-link" (click)="editCPA(data?.capexId)">{{ data?.capexId | number:'4.0' | removeComma }}</a>
                        </td>
                        <td class="text-left text-weight">{{ data?.capexType === 'AIRPLUS_PAPERPLUS' ? 'AP/PP' : 'FP' }}</td>
                        <td class="text-left text-weight">{{ data?.version }}</td>
                        <td class="text-left text-weight">{{ data?.createdBy | titlecase }}
                          <span class="approver-title" *ngIf="data?.creatorTitle">{{data?.creatorTitle === 'VP (Sales)' ? 'VP' : data?.creatorTitle}}</span>
                        </td>
                        <td class="text-left text-weight">{{ data?.capexDate | date: "MM/dd/yyyy"  }}</td>
                        <td class="text-left text-weight">{{ data?.cpaType }}</td>
                        <td *ngIf="title !== titleName.TM && title !== titleName.FPS" class="text-left text-weight action-width">{{ data?.territoryManager }}</td>
                        <td class="text-left text-weight">{{ data?.shipToName }}</td>
                        <td class="text-left text-weight">{{ data?.soldToName }}</td>
                        <td class="text-left text-weight">{{ data?.totalMarginPercent }}%</td>
                        <td class="text-left text-weight pl-0"><span [ngClass]="data?.color === 'YELLOW' ? 'badge badge-secondary' : data?.color === 'GREEN' ?
                      'badge badge-primary' : 'badge badge-danger'">{{ data?.color | uppercase }}</span></td>
                        <td class="text-right text-weight">
                          {{ data?.totalMarginAmount | currency: data?.currencyId === 2 ? appConstants.canadaCurrency : appConstants.currency }}
                        </td>
                        <td class="text-left text-weight"><span class="status-data">{{ data?.approvalStatus | replaceSymbol | uppercase }}</span></td>
                        <td class="text-left text-weighth">{{data?.installationDate | date: "MM/dd/yyyy"  }}</td>
                        <td class="text-left text-weight" *ngIf="title !== titleName.VIEWER_C1">
                          <div>
                            <em class="far fa-edit icon-size" (click)="editCPA(data?.capexId)" [title]="'COMMON.EDIT_CAPEX' | translate"></em>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="col-12" *ngIf="!cpaData?.length">
                    <div class="row">
                      <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
                        {{ 'DASHBOARD.NO_DATA' | translate }}
                      </h6>
                    </div>
                  </div>
                  <div class="col-12" *ngIf="totalCPAElements">
                    <div class="d-flex justify-content-between p-2">
                      <ngb-pagination [collectionSize]="totalCPAElements" [boundaryLinks]="true" [maxSize]="5" [(page)]="viewPageableCPAObj.page" [pageSize]="viewPageableCPAObj.size"
                        (pageChange)="getCPAPagination($event, true)">
                        <ng-template ngbPaginationFirst><em class="fa fa-angle-double-left"></em></ng-template>
                        <ng-template ngbPaginationLast><em class="fa fa-angle-double-right"></em></ng-template>
                        <ng-template ngbPaginationPrevious><em class="fa fa-angle-left"></em></ng-template>
                        <ng-template ngbPaginationNext><em class="fa fa-angle-right"></em></ng-template>
                      </ngb-pagination>
                      <div class="d-flex justify-content-between">
                        <label class="mt-2 mr-3">{{'COMMON.ROWS_PER_PAGE' | translate}}</label>
                        <select class="custom-select" style="width: auto" [(ngModel)]="viewPageableCPAObj.size" (ngModelChange)="getCPAPagination($event, false)">
                          <option [ngValue]="pageNumber" *ngFor="let pageNumber of paginationConfig.pageSizeOptions">{{pageNumber}}</option>
                        </select>
                        <app-show-pagination-elements [pageableObject]="totalPaginationCPAObj"></app-show-pagination-elements>
                      </div>
                    </div>
                  </div>
                </div>
              </kt-portlet-body>
            </kt-portlet>
          </div>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </mat-sidenav-content>
</mat-sidenav-container>
