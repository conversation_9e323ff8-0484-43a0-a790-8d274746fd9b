// USA
const airplusPaperplus = 'AIRplus & PAPERplus';
const integrationSystem = 'Integration System';
const materialCategory = 'Material Category';
const materialGroup = 'Material Group';
const nationalAccount = 'National Account';
const salesGroup = 'Sales Group';
const noData = 'No data found';
const createdBY = 'Created by';
const newVersion = 'Confirm New Version';
const clone = 'Confirm Clone';
const materialDesc = 'Material Description';
const addMaterial = 'Add Material';
const confirmRestore = 'Confirm Restore';
const confirmDelete = 'Confirm Delete';
const bookPrice = 'Book Price';
const materialCategoryRequired = 'Material Category is required';
const totalCostToTerritory = 'Total Cost to Territory';
const invalidNumber = 'Please enter valid number';
const descRequired = 'Please enter description';
const selectCategory = 'Please select material category';
const monthlyCostToTerritory = 'Monthly Cost to Territory';
export const locale = {
  lang: 'en',
  data: {
    TRANSLATOR: {
      SELECT: 'Select your language',
    },
    LOGIN: {
      SIGNIN: 'Sign In',
      WELCOME_TITLE: 'Welcome to Sales Tool',
      WELCOME_SUBTITLE: 'Perfect Protective Packaging',
      LOGIN_SUBTITLE: 'Enter your username and password'
    },
    MENU: {
      NEW: 'new',
      ACTIONS: 'Actions',
      CREATE_POST: 'Create New Post',
      PAGES: 'Pages',
      FEATURES: 'Features',
      APPS: 'Apps',
      DASHBOARD: 'Dashboard',
      USERS: 'Users',
      MASTER_DATA: 'Master Data',
      AP_AND_PP: airplusPaperplus,
      FP: 'FOAMplus',
      EQUIPMENTS: 'Equipment',
      INTEGRATION_SYSTEMS: integrationSystem,
      NON_EQUIPMENT_MATERIAL: 'Non-Equipment',
      GENERAL: 'General',
      MATERIAL_CATEGORY: materialCategory,
      MATERIAL_GROUP: materialGroup,
      MATERIAL: 'Material',
      REMINDER: 'Reminders',
      LOAD_CAPEX: 'Load Capex',
      REPORTS: 'Reports',
      NEW_CAPEX: 'CAPEX',
      AP_PP_CAPEX: airplusPaperplus,
      USER_MANAGEMENT: 'User Management',
      REGION: 'Regions',
      NATIONAL_ACCOUNT: nationalAccount,
      SALES_GROUP: salesGroup,
      FOAMPLUS: 'FOAMplus',
      AUDIT: 'Audit Trail',
      CONFIGURATION: 'Configuration',
      TRANSFER: 'Transfer CAPEX',
      UNIT: 'Unit Of Measure',
      MATERIAL_PROPERTY_LIST: 'Material Property List',
      PLANT: 'Plants'
    },
    PLANT: {
      TITLE: 'Plant List',
      ADD_PLANT: 'Add Plant',
      EDIT_PLANT: 'Edit Plant',
      LABEL: {
        NUMBER: 'Number',
        OFFICE: 'Office'
      },
      VALIDATION: {
        NUMBER: 'Number is required',
        OFFICE: 'Office is required'
      },
      CREATE_SUCCESS: 'Plant is added successfully',
      UPDATE_MESSAGE: 'Plant is updated successfully'
    },
    UNIT_OF_MEASURE: {
      TITLE: 'Unit Of Measure',
      ADD_TITLE: 'Add Unit of Measure',
      EDIT_TITLE: 'Edit Unit of Measure',
      LABEL: {
        NAME: 'Name',
        MATERIAL_CATEGORY: materialCategory,
        MATERIAL_GROUP: materialGroup,
        ABBREVIATION: 'Abbreviation',
        PRICE_PER: 'Price Per',
        CSV_UNIT: 'CSV Unit'
      },
      VALIDATION: {
        NAME: 'Name is required',
        MATERIAL_CATEGORY: 'Material Category is required',
        MATERIAL_GROUP: 'Material Group is required',
        ABBREVIATION: 'Abbreviation is required',
        PRICE_PER: 'Price Per is required',
        INVALID_FORMAT: 'Please enter data in valid format',
        CSV_UNIT: 'CSV Unit is required'
      },
      CREATE_SUCCESS: 'Unit of Measure is added successfully',
      CLONE_SUCCESS: 'Unit of Measure is cloned successfully',
      UPDATE_MESSAGE: 'Unit of Measure is updated successfully',
    },
    REMINDERS: {
      LABEL: {
        QUOTE: '# Quote',
        CAPEX_DATE: 'CAPEX Date',
        CUSTOMER: 'Customer',
        DISTRIBUTOR: 'Distributor',
        AMOUNT: 'Amount',
        EXPIRATION: 'Expiration',
        MARK_AS_READ: 'Mark as Read',
        CAPEX_NUMBER: 'CAPEX Number'
      },
      NO_DATA: noData,
      MODAL: {
        READ_SUCCESS: 'Reminder has been marked as read successfully',
        TITLE: 'Confirm Mark As Read?',
        BODY: 'Are you sure you want to mark this reminder as read?'
      }
    },
    TRANSFER_CAPEX: {
      TITLE: 'Transfer CAPEX Ownership',
      CAPEX_LIST: 'CAPEX Listing',
      BTN: 'Transfer',
      LABEL: {
        TM: 'Select TM/FPS',
        TRANSFER_TM: 'Transfer To',
        OWNER: 'Owner(TM/FPS)'
      },
      VALIDATION: {
        REQUIRED: 'Required'
      },
      TRANSFER_SUCCESS: 'CAPEX has been transferred successfully'
    },
    CONFIGURATION: {
      UPDATE_TITLE: 'Update Configuration',
      GENERAL: {
        KEY: 'Key',
        VALUE: 'Value',
        VALIDATION: {
          VALUE: 'Value is required'
        },
        SUCCESS: 'General Configuration has been updated successfully'
      },
      MARGIN_COLOR: {
        TITLE: 'Margin-Color Rule (AP/PP)',
        FP_TITLE: 'Margin-Color Rule (FP)',
        NE_TITLE: 'Margin-Color Rule (Non-Equipment)',
        LABEL: {
          COLOR: 'Color',
          LOWER_BOUND: 'Lower Bound Value',
          UPPER_BOUND: 'Upper Bound Value'
        },
        VALIDATION: {
          LOWER_BOUND: 'Lower Bound Value is required',
          UPPER_BOUND: 'Upper Bound Value is required',
          INVALID: 'Please enter value in valid number format',
          INVALID_ORDER: 'Upper value should be greater than lower value'
        },
        SUCCESS: 'Margin-Color Rule has been updated successfully'
      },
      CURRENCY: {
        TITLE: 'Currency and Exchange Rate',
        LABEL: {
          NAME: 'Name',
          SYMBOL: 'Symbol',
          ABBREVIATION: 'Abbreviation',
          EXCHANGE_RATE: 'Exchange Rate'
        },
        VALIDATION: {
          EXCHANGE_RATE: 'Exchange Rate is required'
        },
        SUCCESS: 'Currency and Exchange Rates has been updated successfully'
      },
      MATERIAL_PROPERTY: {
        LABEL: {
          NAME: 'Property Name',
          CATEGORY_NAME: 'Material Category Name',
          COST_PER_100: "Cost Per 100'"
        },
        VALIDATION: {
          COST_PER_100: "Cost Per 100' is required"
        }
      },
      MATERIAL_PROPERTY_SUCCESS: 'Material Property has been updated successfully'
    },
    PREVENT_MODAL: {
      TITLE: 'Confirm?',
      BODY: 'You have unsaved changes! If you leave, your changes will be lost. Are you sure?',
      BTN: 'Continue'
    },
    AUTO_LOGOUT: {
      TITLE: 'Warning...',
      SIGN_OUT: 'Sign Out',
      CONTINUE: 'Continue working'
    },
    COMMON: {
      ROWS_PER_PAGE: 'Rows per page :',
      SAVE: 'Save',
      RESET: 'Reset',
      UPDATE: 'Update',
      DELETE: 'Delete',
      DELETE_ALL: 'Delete All',
      CANCEL: "Cancel",
      EXPORT: 'Export',
      EXPORT_ALL: 'Export All',
      IMPORT: 'Import',
      SUBMIT: 'Submit',
      EXCEL: 'Excel',
      CSV: 'CSV',
      BACK_TO_DASHBOARD: 'Back to CAPEX Listing',
      AP_PP_CAPEX: airplusPaperplus,
      FOAM_CAPEX: 'FOAMplus',
      WELCOME: 'Welcome,',
      SIGN_OUT_BTN: 'Sign out',
      CHANGE_PASSWORD_BTN: 'Change Password',
      APPLY_BUTTON: 'Apply',
      RESET_BTN: 'Reset',
      VP_SALES: 'VP OF SALES',
      REGION: 'REGION',
      REGIONAL_SALES_MANAGER: 'REGIONAL SALES MANAGER',
      ADD_MORE: 'Create Another',
      BACK_TO_LOGIN: 'Back to Sign In',
      TOTALS: 'TOTALS',
      SEARCH: 'Search...',
      NO_DATA: noData,
      SAMPLE_TEXT: 'Download sample format:',
      UPLOAD: 'Upload',
      PLEASE_WAIT: 'Please wait...',
      APPROVE: 'Approve',
      REJECT: 'Reject',
      SEND_CS: 'Send to CS',
      ARCHIVED: 'Archived',
      ARCHIVE: 'Archive',
      REASON_FOR_REJECTION: 'Reason for Rejection',
      SHOW_ARCHIVE: 'Show Archived',
      SHOW_DELETED: 'Show Deleted',
      SHARE: 'Download',
      YES: 'Yes',
      NO: 'No',
      RIGHT: 'Right',
      LEFT: 'Left',
      CENTER: 'Center',
      EXPORT_SUCCESS: 'Capex has been exported successfully',
      EXPORT_ALL_SUCCESS: 'Exporting is in progress, you will get an exported Capex download link in your email.',
      ARCHIVE_SUCCESS: 'CAPEX has been archived successfully',
      DOWNLOAD: 'Download',
      PREVIEW: 'Image Preview',
      NONE: 'None',
      SAVE_CUSTOMER: 'Save Customer Facility Details',
      REVERT: 'Revert',
      SUMMARY_WARNING: 'This will automatically turn this CAPEX RED',
      FASTLANE_CAPEX: 'Create Fastlane CAPEX',
      CHANGE: 'Change',
      DELETED_ITEM: {
        DELETED_ROW: 'The item no longer exists, please replace it.',
        RESTRICT_APPROVAL: 'Few items has been deleted from Masters and still present in CAPEX marked as RED, please delete it.',
        IPA_WARNING: 'Items marked in red in Integrations has already been deleted from CAPEX',
        CPA_WARNING: 'Items marked in red in Price to End User section has already been deleted from CAPEX',
        PROPOSAL_WARNING: 'Few items has been deleted from CAPEX that are still present here in Proposal',
        TABLE_MSG: ' Items marked in RED has already been deleted'
      },
      EDIT_CAPEX: 'Edit CAPEX',
      CLONE_CAPEX: 'Clone CAPEX',
      EDIT_IPA: 'Edit IPA',
      DELETE_CAPEX: 'Delete CAPEX',
      MISSING_ITEM: 'Few of the information in either General Info or Summary Section is missing. Please fill the required data and try again.',
      READ_MORE: 'Click to Read More',
      CLEAR_CACHE: 'All Caches has been cleared',
      CLEAR_BTN: 'Clear Cache',
      ERROR_FIELDS: 'Invalid fields: ',
      ERROR_DETAIL: 'Please find more details in the form.',
      CPA_ERROR: 'Add Price To End User below in the form.',
      MACHINE_ERROR: 'Add Machine Charge below in the form.',
      STATUS_VALIDATION: 'Status is required'
    },
    NOTIFICATION: {
      TITLE: 'Notifications',
      NEW: 'New',
      NO_DATA: 'No notifications found',
      FILTER: {
        TWO_DAY: 'Last 2 days',
        ALL_UNREAD: 'All Unread',
        CLEAR_ALL: 'Read All'
      },
      CLEAR: {
        TITLE: 'Clear All Notifications',
        BODY: 'Are you sure you want to mark all notifications as Read?',
        SUCCESS: 'All notifications are marked as read for the user.'
      }
    },
    CAPEX_WORKFLOW: {
      SUBMIT_APPROVAL: {
        TITLE: 'Submit for Approval',
        BODY: 'Are you sure you want to submit CAPEX for approval?'
      },
      SEND_CS: {
        TITLE: 'Send CS',
        BODY: 'Are you sure you want to send CAPEX to CS?'
      },
      ARCHIVE: {
        TITLE: 'Archive',
        BODY: 'Are you sure you want to archive the CAPEX?'
      },
      APPROVE: {
        TITLE: 'Approve',
        BODY: 'Are you sure you want to approve the CAPEX?'
      },
      REVERT: {
        TITLE: 'Revert CAPEX Status',
        BODY: 'Are you sure you want to revert CAPEX status to OPEN?',
        SUCCESS: 'CAPEX status has been reverted successfully to OPEN'
      },
      REVERT_IPA: {
        TITLE: 'Revert IPA Status',
        BODY: 'Are you sure you want to revert IPA status to OPEN?',
        SUCCESS: 'IPA status has been reverted successfully to OPEN'
      },
      DELETE: {
        TITLE: 'Delete CAPEX',
        BODY: 'Are you sure you want to delete CAPEX?',
        SUCCESS: 'CAPEX has been deleted successfully',
        DELETE_ALL: 'All selected CAPEX has been deleted successfully'
      },
      CONFIRM_CURRENCY: {
        TITLE: 'Confirm Currency Change',
        BODY: 'Changing the currency will cause all financial values to be converted to the selected currency! Are you sure you wish to change the currency?'
      },
      CONFIRM_CPA_TYPE_CHANGE: {
        TITLE: 'Confirm CPA Type Change',
        BODY: 'Are you sure you want to Change CPA Type?'
      }
    },
    PROPOSAL: {
      TITLE: 'Proposal',
      UPDATE: 'Update Existing',
      CREATE_VERSION: 'Create New Version',
      DOWNLOAD: 'Download As Word',
      LABEL: {
        ID: 'ID',
        PROPOSAL_DATE: 'Date',
        VERSION: 'Version',
        ACTION: 'Action',
        TABLE: 'Proposal Version',
        FILE_NAME: 'File',
        CREATED_BY: createdBY
      },
      UPDATE_SUCCESS: 'Proposal updated successfully',
      DOWNLOAD_SUCCESS: 'Proposal downloaded successfully',
      VERSION_SUCCESS: 'New Version of Proposal is added successfully',
      NO_VERSION: 'No Proposal Version found',
      CLONE_SUCCESS: 'Proposal cloned successfully',
      VERSION_MODAL: {
        TITLE: newVersion,
        BODY: 'This will create a blank and new version of Proposal. Are you sure you want to proceed?'
      },
      CLONE_MODAL: {
        TITLE: clone,
        BODY: 'This will create a duplicate version of Proposal. Are you sure you want to proceed?'
      }
    },
    AP_PP_CPA: {
      TITLE: 'CPA',
      DOWNLOAD_PDF: 'Download As PDF',
      ADD_SHIP_LOC: 'Add New Ship To Location',
      UPDATE_SHIP_LOC: 'Edit New Ship To Location',
      SUCCESS: 'Ship to Location has been added successfully',
      UPDATE: 'Ship to Location has been updated successfully',
      DELETE_LOC: {
        BODY: 'Are you sure you want to delete this ship to location?',
        MSG: 'Ship to Location has been deleted successfully'
      },
      LABEL: {
        CPA_TYPE: 'CPA Type',
        CPA_VERSION_TYPE: 'CPA Version Type',
        FRIEGHT_CHARGE: 'Freight Charged To',
        SHIP_TO: 'Ship To',
        SOLD_TO: 'Sold To',
        LANGUAGE: 'Select Language',
        NA: nationalAccount,
        MULTIPLE_SHIP: 'Multiple Ship To Locations',
        SHIP_NO: 'Ship To No.',
        ADDRESS: 'Street Address',
        CITY: 'City',
        STATE: 'State',
        ZIPCODE: 'Zipcode',
        MACHINE_CHARGE_TO: 'Machine Charge To',
        ACTION: 'Actions'
      },
      FORM_VALIDATION: {
        SHIP_NO: 'Ship To No. is required',
        SHIP_NO_INVALID: 'Please enter Ship To No. in valid format',
        ADDRESS: 'Street Address is required',
        ADDRESS_INVALID: 'Please enter Street Address in valid format',
        CITY: 'City is required',
        CITY_INVALID: 'Please enter City in valid format',
        STATE: 'State is required',
        STATE_INVALID: 'Please enter State in valid format',
        ZIPCODE: 'Zipcode is required',
        ZIPCODE_INVALID: 'Please enter Zipcode in valid format',
      },
      CPA_HEADER: {
        TITLE: 'CPA Header',
        LABEL: {
          SHIP_NAME: 'Ship To Contact Name',
          SOLD_NAME: 'Sold To Contact Name',
          SHIP_ADDRESS: 'Ship To Street Address',
          INITIAL_TERM: 'Initial Term',
          SHIP_CITY: 'Ship To City',
          INSTALLATION_DATE: 'Effective Date',
          SHIP_STATE: 'Ship To State',
          MIN_QTY: 'Minimum Order Quantity',
          SHIP_ZIPCODE: 'Ship To Zipcode',
          LEAD_TIME: 'Lead Times',
          TERMS: 'Add Terms & Conditions',
          INC_PGT: 'Increment after Initial Price Guarantee Term'
        }
      },
      CPA_INTEGRATION: {
        TITLE: 'CPA Integration',
        BODY: 'Customer will lease the Custom Integrations over the Initial Term for a total amount, to be paid by customer monthly.'
      },
      CPA_MACHINE: {
        TITLE: 'CPA Machine',
        BODY: 'Additionals machines can be requested by the Customer for a Charge, per machine per month.'
      },
      PRICE_END_USER: {
        TITLE: 'Price to the End User(Ship To)',
        LABEL: {
          MATERIAL: 'Material #',
          DESC: materialDesc,
          UNIT_TO_DIST: "Roll or Unit Price to Distributor",
          UNIT_TO_END_USER: "Roll or Unit Price to End User",
          MATERIAL_TYPE: 'Material Type',
          UNIT_DIST: 'Price to Distributor',
          UNIT_END_USER: 'Price to End User'
        }
      },
      MACHINE_CHARGE: {
        TITLE: 'Machine Charged To',
        LABEL: {
          EQUIPMENT: 'Equipment Number',
          DESC: 'Description',
          QUANTITY: 'Quantity',
          CHARGE: 'Charge',
          CUSTOMER_CHARGE: 'Customer Charge',
          MACHINE_CHARGE: 'Machine Charge',
          TITLE_LABEL: 'Title',
          MONTHS_ALLOCATED: 'Months Allocated',
          SALES_PRICE: 'Sales Price',
          MACHINE_CHARGE_TO_DISTRIBUTOR: 'Machine Charge To Distributor',
          MACHINE_CHARGE_TO_ENDUSER: 'Machine Charge To End User',
          MONTHS_ALLOCATED_TO_DISTRIBUTOR: 'Months Allocated To Distributor',
          MONTHS_ALLOCATED_TO_ENDUSER: 'Months Allocated To End User',
        }
      },
      CPA_PICTURES: {
        TITLE: 'CPA Integration Pictures',
        INSERT_BTN: 'Insert',
        NO_PIC_FOUND: 'No Pictures Found',
        IPA_TITLE: 'IPA Integration Pictures'
      },
      ADD_PIC: {
        TITLE: 'Insert Integration Pictures',
        BROWSE_IMG: 'Browse Your Images',
        IMG_SUB_TEXT: '.PNG & .JPEG Files Only, Max File Size : 5MB, Recommended Image Size : 1024x768',
        CAPTION: 'Enter Caption',
        SUCCESS: 'Integration Picture added successfully',
        ADD_IMAGE: 'Please insert picture',
        MAX_LIMIT: 'Maximum limit for inserting pictures has reached!',
        CAPTION_REQD: 'Please enter caption'
      },
      DEL_PIC: {
        BODY: 'Are you sure you want to delete this integration picture?',
        SUCCESS: 'Integration picture has been deleted successfully'
      },
      VALIDATION: {
        CPA_TYPE: 'Please select CPA Type',
        MACHINE_CHARGE_TO: 'Please select Machine Charge To',
        LANGUAGE_REQD: 'Please select language',
        SHIP_NAME: 'Ship To Contact Name is required',
        INVALID_SHIP_NAME: 'Please enter Ship To Contact Name in valid format',
        SOLD_NAME: 'Sold To Contact Name is required',
        INVALID_SOLD_NAME: 'Please enter Sold To Contact Name in valid format',
        SHIP_ADDRESS: 'Ship To Street Address is required',
        SHIP_ADDRESS_INVALID: 'Please enter Ship To Street Address in valid format',
        INITIAL_TERM: 'Initial Term is required',
        INITIAL_TERM_INVALID: 'Enter a number from 6 to 36',
        SHIP_CITY: 'Ship To City is required',
        SHIP_CITY_INVALID: 'Please enter Ship To City in valid format',
        INSTALLATION_DATE: 'Effective Date is required',
        SHIP_STATE: 'Ship To State is required',
        SHIP_STATE_INVALID: 'Please enter Ship To State in valid format',
        MIN_QTY: 'Minimum Order Quantity is required',
        MIN_QTY_INVALID: 'Please enter Minimum Order Quantity in valid format',
        SHIP_ZIPCODE: 'Ship To Zipcode is required',
        SHIP_ZIPCODE_INVALID: 'Please enter Ship To Zipcode in valid format',
        LEAD_TIME: 'Lead Times is required',
        LEAD_TIME_INVALID: 'Please enter Lead Times in valid format',
        INC_PGT: 'Increment after Initial Price Guarantee Term is required',
        INC_PGT_INVALID: 'Please enter Increment after Initial Price Guarantee Term in between 0-10',
        VALID_PRICE: 'Please enter amount in valid format',
        UNIT_TO_END_USER: "Roll or Unit Price to End User is required",
        UNIT_END_USER: "Price to End User is required",
        MACHINE_CHARGE: 'Machine Charge is required',
        MONTHS_ALLOCATED: 'Months Allocation is required',
        SALES_PRICE: 'Sales Price is required'
      },
      VERSION: {
        ID: 'ID',
        DATE: 'Date',
        VERSION: 'Version',
        ACTION: 'Action',
        TABLE: 'CPA Version',
        FILE_NAME: 'File',
        CREATED_BY: createdBY,
        NO_VERSION: 'No Version found'
      },
      SUCCESS_MSG: 'CPA created successfully',
      UPDATE_MSG: 'CPA updated successfully',
      FILE_ERROR: 'Please enter file in jpg, jpeg or png format and file size should be less than 5MB',
      FILE_UPLOAD_ERROR: 'Please enter file of size less than 1MB',
      MATERIAL_ADDED: 'Price to End User is added successfully',
      MACHINE_ADDED: 'Machine Charge To is added successfully',
      VERSION_SUCCESS: 'New version has been created successfully',
      CLONE_SUCCESS: 'CPA has been cloned successfully',
      DOWNLOAD_SUCCESS: 'CPA has been downloaded successfully',
      VERSION_MODAL: {
        TITLE: newVersion,
        BODY: 'This will create a blank and new version of CPA. Are you sure you want to proceed?'
      },
      CLONE_MODAL: {
        TITLE: clone,
        BODY: 'This will create a duplicate version of CPA. Are you sure you want to proceed?'
      },
      DOC_ERROR: 'Please enter file in valid format and per file size should be max 10MB and total files size should be max 50MB.'
    },
    FP_CPA: {
      MIN_ORDER: 'Minimum Order Size',
      MIN_ORDER_REQD: 'Minimum Order Size is required',
      INVALID_MIN_ORDER: 'Please enter Minimum Order Size in valid format',
      MACHINE_CUSTOMER: 'All Storopack machines are placed at the Customer with an expectation of',
      CONSUME_MONTH: 'consumed per month per machine',
      ADDITIONAL_MACHINE: 'Additional machines can be requested by the Customer for a charge of',
      MONTH_MACHINE: 'per month per machine',
      LEASE: 'Lease',
      LEASE_OWN: 'Lease To Own',
      MACHINE_SALE: 'Machine Sale'
    },
    IPA: {
      TITLE: 'IPA',
      VERSION: 'New IPA Version has been created successfully',
      CLONE: 'IPA has been cloned successfully',
      VERSION_TITLE: 'IPA Version',
      DOWNLOAD_SUCCESS: 'IPA has been downloaded successfully',
      IPA_DOWNLOAD: 'Integration Picture has been downloaded successfully',
      VERSION_MODAL: {
        TITLE: newVersion,
        BODY: 'This will create a blank and new version of IPA. Are you sure you want to proceed?'
      },
      CLONE_MODAL: {
        TITLE: clone,
        BODY: 'This will create a duplicate version of IPA. Are you sure you want to proceed?'
      },
      SUBMIT_APPROVAL: {
        BODY: 'Are you sure you want to submit IPA for approval?',
        SUCCESS: 'IPA has been sent for approval successfully'
      },
      CUSTOM_INTEGRATION: {
        TITLE: 'Customer Integration Request - Contact Information',
        ADD_SUCCESS: 'Custom Integration System is added successfully',
        UPDATE_SUCCESS: 'Custom Integration System is updated successfully',
        LABEL: {
          TM: 'TM',
          REQUEST_DATE: 'Request Date',
          NAM: 'NAM',
          CITY: 'City',
          STATE: 'State',
          DESIGN_ENGINEER: 'Design Engineer',
          COUNTRY: 'Country',
          ZIPCODE: 'Zipcode',
          RSM: 'RSM',
          FST: 'FST',
          COMPANY: 'Company Name',
          FULL_ADDRESS: 'Street Address',
          CONTACT: 'Contact Name',
          PHN_EMAIL: 'Contact Phone',
          TOTAL_COST: 'Total Cost'
        },
        VALIDATION: {
          TM_REQD: 'TM is required',
          DATE: 'Request Date is required',
          NAM: 'NAM is required',
          DESIGN_ENGINEER: 'Design Engineer is required',
          DESIGN_ENGINEER_INVALID: 'Please enter Design Engineer in valid format',
          RSM: 'RSM is required',
          FST: 'FST is required',
          FST_INVALID: 'Please enter FST in valid format',
          COMPANY: 'Company Name is required',
          COMPANY_INVALID: 'Please enter Company Name in valid format',
          FULL_ADDRESS: 'Street Address is required',
          FULL_ADDRESS_INVALID: 'Please enter Street Address in valid format',
          CITY: 'City is required',
          CITY_INVALID: 'Please enter City in valid format',
          STATE: 'State is required',
          STATE_INVALID: 'Please enter State in valid format',
          ZIPCODE: 'Zipcode is required',
          ZIPCODE_INVALID: 'Please enter Zipcode in valid format',
          COUNTRY: 'Country is required',
          CONTACT: 'Contact Name is required',
          CONTACT_INVALID: 'Please enter Contact Name in valid format',
          PHN_EMAIL: 'Phone is required',
          PHN_EMAIL_INVALID: 'Please enter Phone in valid format',
        },
        SUCCESS_MSG: 'IPA created successfully',
        UPDATE_MSG: 'IPA updated successfully',
        DELETE: {
          BODY: 'Are you sure you want to delete this Integration Question?',
          SUCCESS: 'Integration Question has been deleted successfully'
        }
      },
      PRODUCT_INFO: {
        TITLE: 'Product & Machine Information',
        OVERHEAD_HOPPER: 'Overhead Hopper Integration Questions',
        MATERIAL_TITLE: 'MATERIAL Used with Integration System',
        MACHINE_TITLE: 'MACHINES Used with Integration System',
        SELECT_MATERIAL: 'Select Material',
        SELECT_MACHINE: 'Select Machine',
        ADD_MATERIAL: addMaterial,
        ADD_MACHINE: 'Add Machine',
        LABEL: {
          ROLLS: 'Rolls per Month',
          USED_SYSTEM: 'Used with System?',
          MACHINE: 'Machine #',
          QTY: 'Quantity',
          ACTION: 'Action'
        },
        DELETE_DESC: 'Are you sure you want to delete IPA Material?',
        DELETE_SUCCESS: 'IPA Material has been deleted successfully',
        DELETE_EQUIPMENT_DESC: 'Are you sure you want to delete IPA Equipment?',
        DELETE_EQUIPMENT_SUCCESS: 'IPA Equipment has been deleted successfully',
        NO_DATA: noData,
        SUCCESS_ADD: 'Overhead Hopper Integration Question has been added successfully',
        SUCCESS_UPDATE: 'Overhead Hopper Integration Question has been updated successfully'
      },
      INTEGRATION_SPECIFICATION: {
        TITLE: 'Integration Specifications',
        INSTRUCTION: '** Will not be mandatory if "Workstations / Others" is different from N.A. or blank',
        INCH: 'in.',
        ADD_SUCCESS: 'Integration Specifications is added successfully',
        UPDATE_SUCCESS: 'Integration Specifications is updated successfully',
        LABEL: {
          WORKSTATION_OTHER: 'Workstation/Others',
          UPPER_RACK: 'Upper Rack',
          WORKSTATION: 'Workstation (LxWxH)',
          UPPER_BOX_DIVIDER: 'Upper Rack Box Dividers',
          WORKSTATION_REQUESTED: 'Number of Workstations Requested',
          NO_OF_DIVIDER: 'Number of Dividers on Upper Rack',
          DIST_TABLE: 'Distance between Tables',
          UPPER_SHELF: 'Upper Shelf',
          TABLE_HEIGHT: 'Table height auto adj.',
          CABLE: 'Cable Management',
          HOPPER_TYPE: 'Hopper Type',
          LOWER_SHELF: 'Lower mid-Level Shelf',
          DIVIDER: 'Number of Dividers on Shelf',
          DRAWER_REQD: 'Drawer(s) required under table top',
          DRAWERS: 'Number of Drawers',
          MONITER: 'Monitor/Keyboard Arm?',
          HOPPER_SIZE: 'Hopper Size',
          UPPER_PRINTER: 'Upper Shelf Printer Pull-Out',
          LOWER_PRINTER: 'Lower Shelf Printer Pull-Out',
          FRAME_TYPE: 'Frame Type',
          NOTES: 'Describe limitations to the System or other info not covered above',
          OPERATIONS: '# of Operators',
          SYSTEMS: '# of Systems',
          LIFT: 'Lift or Venturi',
          PLEXI: '2" Mesh or Plexi',
          COIL_SHELF: 'Coil Sling on Hopper (add qty & size in notes)',
          SHELF_FRAME: 'Shelf on Frame',
          COIL_SLING: 'Coil Sling on Hopper',
          UPPER_RACK_LABEL: 'Upper Rack'
        },
        VALIDATION: {
          WORKSTATION_OTHER: 'Workstation/Others is required',
          UPPER_RACK: 'Workstation Upper Rack is required',
          WORKSTATION: 'Workstation (LxWxH) is required',
          INVALID_WORKSTATION: 'Invalid format',
          UPPER_BOX_DIVIDER: 'Upper Rack Box Dividers is required',
          WORKSTATION_REQUESTED: 'Number of Workstations Requested is required',
          INVALID_WORKSTATION_REQUESTED: 'Please enter Number of Workstations Requested in valid format',
          NO_OF_DIVIDER: 'Number of Dividers is required',
          INVALID_NO_OF_DIVIDER: 'Please enter Number of Dividers in valid format',
          DIST_TABLE: 'Distance between Tables is required',
          INVALID_DIST_TABLE: 'Please enter Distance between Tables in valid format',
          UPPER_SHELF: 'Upper Shelf is required',
          TABLE_HEIGHT: 'Table height auto adj. is required',
          CABLE: 'Cable Management is required',
          HOPPER_TYPE: 'Hopper Type is required',
          LOWER_SHELF: 'Lower mid-Level Shelf is required',
          DIVIDER: 'Number of Dividers is required',
          INVALID_DIVIDER: 'Please enter Number of Dividers in valid format',
          DRAWER_REQD: 'Drawer(s) required under table top is required',
          DRAWERS: 'Number of Drawers is required',
          INVALID_DRAWERS: 'Please enter Number of Drawers in valid format',
          MONITER: 'Monitor/Keyboard Arm is required',
          HOPPER_SIZE: 'Hopper Size(L-W-H) is required',
          INVALID_HOPPER_SIZE: 'Please enter value in valid format',
          UPPER_PRINTER: 'Upper Shelf Printer Pull-Out is required',
          LOWER_PRINTER: 'Lower Shelf Printer Pull-Out is required',
          FRAME_TYPE: 'Frame Type is required',
          NOTES: 'Please enter limitation in valid format',
          OPERATIONS: '# of Operators is required',
          INVALID_OPERATIONS: 'Please enter # of Operators in valid format',
          SYSTEMS: '# of Systems is required',
          INVALID_SYSTEMS: 'Please enter # of Systems in valid format',
          LIFT: 'Lift or Venturi is required',
          INVALID_LIFT: 'Please enter Lift or Venturi in valid format',
          PLEXI: '2" Mesh or Plexi is required',
          INVALID_PLEXI: 'Please enter 2" Mesh or Plexi in valid format',
          COIL_SHELF: 'Coil Sling on Hopper is required',
          SHELF_FRAME: 'Shelf on Frame is required',
          NOTES_REQD: 'Notes is required'
        }
      },
      CUSTOMER_LAYOUT: {
        TITLE: 'Customer Facility Details',
        ITEM: 'ITEMS',
        LEN: 'L',
        WIDTH: 'W',
        HEIGHT: 'H',
        FT: 'ft.',
        UPDATE_SUCCESS: 'Customer Facility Details has been updated successfully',
        LABEL: {
          HOPPER: 'Hopper',
          CONVEYOR: 'Conveyor',
          PACK_TABLE: 'Current Pack Table',
          SHELF: 'Current Shelf',
          BOX: 'Current Box Rack',
          HOPPER_HEIGHT: 'Height from Floor to bottom of Hopper',
          CLEARANCE_HEIGHT: 'Clearance height-conveyor to Hopper frame',
          ROLL_HEIGHT: 'Clearance height to Bottom of Roll',
          LOADING_HEIGHT: 'Material Loading Height',
          GAP_TABLE: 'Gap between Tables or box or racks',
          LARGE_CARTON: 'Largest Carton',
          SMALL_CARTON: 'Smallest Carton',
          ADJ_TABLE_HEIGHT: 'Is Table Height adjustable',
          SAME_HEIGHT: 'Are all the table heights are same',
          FLAPS: 'Flaps up or down'
        },
        VALIDATION: {
          INVALID_VALUE: 'Please enter value in valid format',
          FLAPS: 'Flaps up or down is required'
        }
      },
      UNDERTABLE_CART: {
        TITLE: 'Undertable / Carts Integration Questions',
        LABEL: {
          SHELF_CART: 'Shelf On Cart',
          TABLE_TOP: 'Table Top Size',
          CLEARANCE_TABLE: 'Clearance under table',
          BOX_CART: '# of Box Dividers on Cart',
          MAIL_BOX: 'Mail Box',
          MAIL_BOX_CART: 'Mail Box on Cart or Table',
          NOTES: 'Describe limitations to the System or other info not covered above'
        },
        VALIDATION: {
          MAIL_BOX_CART: 'Mail Box on Cart or Table is required'
        },
        SUCCESS_ADD: 'Undertable/Carts Integration Question has been added successfully',
        SUCCESS_UPDATE: 'Undertable/Carts Integration Question has been updated successfully'
      },
      SMARTLINE: {
        TITLE: 'SMARTline Integration Questions',
        LABEL: {
          TABLE_TYPE: 'Table Type',
          PACK_TABLE: 'Number of Pack Tables',
          RIGHT_VERSION: 'Right & Left Hand Version',
          SCALE_RECESS_SIZE: 'Scale Recess Size',
          SCALE_RECESS: 'Scale Recess',
          REMOTE_DISPLAY: 'Remote Scale Display'
        },
        VALIDATION: {
          TABLE_TYPE: 'Table Type is required'
        },
        SUCCESS_ADD: 'SMARTline Integration Question has been added successfully',
        SUCCESS_UPDATE: 'SMARTline Integration Question has been updated successfully'
      },
      WAREHOUSE: {
        TITLE: 'Warehouse Details',
        GENERAL_QUESTION: 'GENERAL QUESTIONS',
        UPDATE_SUCCESS: 'Warehouse Details is updated successfully',
        LABEL: {
          INBOUND: 'Inbound Conveyor',
          OUTBOUND: 'Outbound Conveyor',
          LIFT_GATE: 'Lift Gate',
          PACK_STATION: 'Pack Station Floor Area',
          SCALE: 'Scale',
          SCALE_TABLE: 'Scale Table',
          CART: 'Cart',
          HOPPER_BOLTED: 'Can Hoppers get Bolted to the floor',
          PACK_BOLTED: 'Pack Tables Bolted to the Floor',
          CONVEYOR_BOLTED: 'Conveyors Bolted to the Floor',
          OBSTRUCTION: 'Any Obstructions',
          TOTE_SMALL: 'Totes-Small (L-W-H)',
          TOTE_MED: 'Totes-Medium (L-W-H)',
          TOTE_LARGE: 'Totes-Large (L-W-H)',
          NOTES: 'Describe the Pack Station Floor Area, and Other customer facility details here'
        }
      },
      CONCEPT_SAM: {
        TITLE: 'System and Module(SAM) Integration Questions',
        LBS: 'lbs',
        UPDATE_SUCCESS: 'Concept SAMs is updated successfully',
        LABEL: {
          NO_CUST_CARTON: 'Number of Customer Carton Sizes',
          MIN_CARTON_WEIGHT: 'Minimum Carton Weight',
          MAX_CARTON_WEIGHT: 'Maximum Carton Weight',
          AVG_CARTON: 'Average Carton per Minute',
          EXISTING_ZAP: 'Existing ZPA Conveyor',
          NO_ZONES: 'Number of Conveyor Zones',
          DS_MOUNT: '3DS Mounting Type',
          LENGTH_CONVEYOR: 'Length of Conveyor Section to Replace?',
          INSIDE_CONVEYOR: 'Inside Conveyor Width',
          ROLLER_SPACING_CENTER: 'Roller Spacing Center to Center',
          MAX_CARTON_SIZE: 'Maximum Carton Size',
          MIN_CARTON_SIZE: 'Minimum Carton Size',
          TOTAL_CONVEYOR_WIDTH: 'Total Conveyor Width & Height',
          ZONE_CONVEYOR: 'Can Zones be added to Conveyor?',
          OBSTRUCTION: 'Obstructions under Conveyor?',
          CONVEYOR_ROLLER: 'Conveyor Height to Top of Roller',
          FPM: 'Conveyor Speed FPM',
          CARTON_JUSTIFIED: 'The Carton Justified on Conveyor?',
          HOW: 'How?',
          NOTE: 'Concept SAMs Additional Notes'
        },
        SUCCESS_ADD: 'System and Module(SAM) Integration Question has been added successfully',
        SUCCESS_UPDATE: 'System and Module(SAM) Integration Question has been updated successfully'
      },
      COMMENT_QUOTE: {
        TITLE: 'Comments & Quotes',
        LABEL: {
          SYS_ID: 'System #',
          SYS_COST: 'System Cost',
          SYS_DESC: 'System Description',
          PROJECT_NUMBER: 'Project #'
        },
        VALIDATION: {
          SYS_COST: 'System Cost is required',
          DESIGN_ENGINEER: 'Design Engineer is required',
          PROJECT_NUMBER: 'Project # is required',
          SYS_DESC: 'System Description is required',
          SYS_ID: 'System # is required'
        },
        DESING_ENGINEER: 'Please enter Design Engineer & Project # in top section of IPA',
        ADD_SUCCESS: 'Comment and Quotes has been added successfully'
      }
    },
    EXPORT: {
      SUCCESS_MSG: 'Data has been exported successfully'
    },
    IMPORT: {
      INSERT_SUCCESS_MSG: 'Row/Rows has been added successfully',
      UPDATE_SUCCESS_MSG: 'Row/Rows has been updated successfully',
      IMPORT_CLOUD: 'Import from System',
      FILE_FORMAT_ERROR: 'Please select file of excel or csv format',
      LABEL: {
        LINE_NUMBER: 'Row Number:',
        COLUMN_NAME: 'Column Name:'
      },
      SELECT_FILE: 'Choose file to upload:',
      RESULT: 'Import Result:',
      NOTES: 'Notes:',
      NOTE_TEXT: 'Sequence and number of fields should be maintained as per sample format.',
      NOTE_TEXT_EXCEL_LENGTH: 'Please upload up to 2400 records only in single excel/csv file.',
      HEADERS: {
        MISSING_HEADER: 'Missing Headers',
        REQUIRED_HEADER: 'Required Headers',
        FILE_HEADER: 'File Headers',
        EXTRA_HEADERS: 'Extra Headers'
      }
    },
    RESTORE: {
      SALES_GROUP: {
        TITLE: confirmRestore,
        DESC: 'This sales group was deleted. Are you sure you want to restore this sales group?',
        RESTORE_BTN: 'Restore',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Sales Group has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring sales group. Please try again'
      },
      ACCOUNT: {
        TITLE: confirmRestore,
        DESC: 'This national account was deleted. Are you sure you want to restore this national account?',
        RESTORE_BTN: 'Restore',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'National Account has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring national account. Please try again'
      },
      MATERIAL: {
        TITLE: confirmRestore,
        DESC: 'This material was deleted. Are you sure you want to restore this material?',
        SUCCESS_MSG: 'Material has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring material. Please try again'
      },
      UNIT: {
        TITLE: confirmRestore,
        DESC: 'This unit of measure was deleted. Are you sure you want to restore this unit of measure?',
        SUCCESS_MSG: 'Unit of Measure has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring unit of measure. Please try again'
      },
      EQUIPMENT: {
        TITLE: confirmRestore,
        DESC: 'This equipment was deleted. Are you sure you want to restore this equipment?',
        SUCCESS_MSG: 'Equipment has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring equipment. Please try again'
      },
      INTEGRATION_SYSTEM: {
        TITLE: confirmRestore,
        DESC: 'This integration system was deleted. Are you sure you want to restore this integration system?',
        SUCCESS_MSG: 'Integration system has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring integration system. Please try again'
      },
      NON_EQUIPMENT: {
        TITLE: confirmRestore,
        DESC: 'This non-equipment material was deleted. Are you sure you want to restore this non-equipment material?',
        SUCCESS_MSG: 'Non-Equipment Material has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring non-equipment material. Please try again'
      },
      USER: {
        TITLE: confirmRestore,
        DESC: 'This user was deleted. Are you sure you want to restore this user?',
        SUCCESS_MSG: 'User has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring user. Please try again',
      },
      PLANT: {
        TITLE: confirmRestore,
        DESC: 'This plant was deleted. Are you sure you want to restore this plant?',
        SUCCESS_MSG: 'Plant has been restored successfully',
        ERROR_MSG: 'There was some issue while restoring plant. Please try again'
      }
    },
    AUDIT_LOGS: {
      TITLE: 'Audit Logs',
      LABEL: {
        SECTION: 'Module Name',
        ID: 'ID',
        MODIFIED_DATE: 'Action Date',
        ACTION: 'Action',
        MODIFIED_BY: 'Action By',
        OLD_DATA: 'Old Data',
        NEW_DATA: 'New Data',
        NOTES: 'Notes'
      },
      SECTION_LIST: {
        USER: 'User',
        AP_PP_MATERIAL: 'AirPlusPaperPlus Material',
        EQUIPMENT: 'Equipment',
        INTEGRATION: integrationSystem,
        FP_MATERIAL: 'Foamplus Material',
        NON_EQUIPMENT: 'Non-Equipment Material',
        REGION: 'Region',
        NATIONAL_ACCOUNT: 'National Account Program',
        SALES_GROUP: salesGroup,
        CURRENCY: 'Currency',
        FREIGHT_VERBIAGE: 'Freight Verbiage',
        QUOTE_ROUTING: 'QuoteRouting Rule Margin',
        INTEGRATION_CATALOG: 'Standard Integration Catalog',
        CONFIG_DATA: 'Config Data'
      },
      ACTION_LIST: {
        SAVE: 'Add',
        DELETE: 'Delete',
        UPDATE: 'Update',
        SOFT_DELETE: 'Soft-delete',
        RESTORE: 'Restore'
      },
      NO_AUDIT: 'No Audit Logs found'
    },
    PROFILE: {
      PROFILE_TITLE: 'User Profile',
      CHANGE_PASSWORD: 'Change Password',
      SIGN_OUT: 'Sign Out'
    },
    DASHBOARD: {
      OPEN: 'Open',
      APPROVED: 'Approved',
      REJECTED: 'Rejected',
      PENDING: 'Pending',
      CLOSED: 'Archived',
      OPEN_TITLE: 'Open CAPEX',
      APPROVED_CAPEX: 'Approved CAPEX',
      REJECTED_CAPEX: 'Rejected CAPEX',
      QUOTE_TITLE: 'Quotes',
      OPEN_TAB: 'Open CAPEX in new tab',
      FILTER: {
        SEARCH_CUSTOMER: 'Search by Customer',
        SEARCH_SOLD: 'Search by Sold To',
        SEARCH_SHIP: 'Search by Ship To',
        STATUS: 'Status',
        DATE: 'Date',
        TM: 'TM/FPS',
        SEARCH_SHIP_NAME: 'Search by Ship To Name',
        SEARCH_PROJECT: 'Search by Project #'
      },
      LABEL: {
        ID: '#CAPEX',
        DEVIATION_DATE: 'Deviation Date',
        CAPEX_DATE: 'Created',
        CREATOR: 'Creator',
        CUSTOMER: 'Customer',
        DISTRIBUTOR: 'Distributor',
        MARGIN: 'Margin %',
        RAG: 'RYG',
        AMOUNT: 'Amount',
        APPROVER: 'Approver',
        STATUS: 'Status',
        ACTION: 'Action',
        MODIFIED_ON: 'Effective Date',
        TYPE: 'Type'
      },
      NO_DATA: 'No Capex Found',
      SEND_APPROVAL: 'CAPEX has been sent successfully for approval',
      REJECT_CAPEX: {
        TITLE: 'Reason for Rejection',
        REASON_REQUIRED: 'Please Enter Reason',
        REASON_SUCCESS: 'CAPEX has been rejected successfully'
      },
      SHARE_CAPEX: {
        TITLE: 'Share CAPEX',
        SELECT_TM: 'Select TM/FPS',
        SELECT_VALIDATION: 'TM/FPS is required',
        SHARE_SUCCESS: 'CAPEX has been shared successfully'
      },
      SEND_CS_SUCCESS: 'CAPEX has been sent to CS successfully',
      CLOSE_CAPEX: 'CAPEX has been archived successfully'
    },
    IPA_DASHBOARD: {
      TITLE: 'IPA Listing',
      CPA_TITLE: 'CPA Listing',
      LABEL: {
        ID: '#IPA',
        VERSION: 'IPA Version',
        CAPEX_NO: '#CAPEX',
        REQUEST_DATE: 'Request Date',
        CREATED_BY: createdBY,
        CREATED: 'Created on',
        STATUS: 'Status',
        ACTION: 'Action',
        CLOSE: 'Submit To Production',
        SHIP_TO: 'End User(Ship To) Name'
      },
      NO_IPA: 'No IPA found',
      CLOSE_IPA: 'IPA has been submitted to production successfully',
      CANCEL_IPA: 'IPA has been cancelled successfully',
      CANCEL_IPA_MODAL: {
        TITLE: 'Cancel IPA',
        CANCEL_BODY: 'Are you sure you want to cancel IPA?',
        BODY: 'IPA is in OPEN status and that will be cancelled if CAPEX will be submitted for approval. Are you sure you want to continue?',
        BTN: 'Proceed'
      },
      CLOSE_IPA_MODAL: {
        TITLE: 'Submit IPA for Production',
        BODY: 'Are you sure you want to submit IPA for production?'
      }
    },
    CAPEX_AUDIT: {
      LABEL: {
        CAPEX_ID: 'ID',
        REJECTION_REASON: 'Rejection Reason'
      }
    },
    AUTH: {
      GENERAL: {
        OR: 'Or',
        SUBMIT_BUTTON: 'Submit',
        NO_ACCOUNT: 'Don\'t have an account?',
        SIGNUP_BUTTON: 'Sign Up',
        FORGOT_BUTTON: 'Forgot Password?',
        BACK_BUTTON: 'Back',
        PRIVACY: 'Privacy',
        LEGAL: 'Legal',
        CONTACT: 'Contact',
        CANCEL: 'Cancel'
      },
      LOGIN: {
        TITLE: 'Login Account',
        BUTTON: 'Sign In',
      },
      FORGOT: {
        TITLE: 'Forgotten Password?',
        DESC: 'Enter your email to reset your password',
        SUCCESS: 'Your account has been successfully reset.'
      },
      REGISTER: {
        TITLE: 'Sign Up',
        DESC: 'Enter your details to create your account',
        SUCCESS: 'Your account has been successfuly registered.'
      },
      RESET_PASSWORD: {
        TITLE: 'Reset Your Password',
        LABEL: {
          NEW_PASSWORD: 'New Password',
          CONFIRM_PASSWORD: 'Confirm New Password',
          CURRENT_PASSWORD: 'Current Password'
        },
        VALIDATION: {
          NO_MATCH: 'Password doesn`t match',
          NO_PASSWORD_MATCH: 'Current password should not match with new password',
          PASSWORD_PATTERN: '(Min 4 chars, atleast 1 capital, 1 small, 1 digit and 1 special character(#,$,@,&))'
        },
        SUCCESS: 'Your account has been successfully reset.',
        SUBMIT: 'Reset Password'
      },
      SIGNOUT: {
        SIGNOUT_TITLE: 'Confirm sign out',
        SIGNOUT_BODY: 'Are you sure you want to sign out from application?'
      },
      INPUT: {
        EMAIL: 'Email Address',
        FULLNAME: 'Fullname',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: 'Username or Email Address'
      },
      VALIDATION: {
        INVALID: 'Please enter valid {{name}}',
        REQUIRED: 'Please enter {{name}}',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        AGREEMENT_REQUIRED: 'Accepting terms & conditions are required',
        NOT_FOUND: 'The requested {{name}} is not found',
        INVALID_LOGIN: 'The login detail is incorrect',
        REQUIRED_FIELD: 'Required',
        MIN_LENGTH_FIELD: 'Minimum field length:',
        MAX_LENGTH_FIELD: 'Maximum field length:',
        INVALID_FIELD: 'Field is not valid',
        USERNAME_REQUIRED: 'Username is required',
        INVALID_EMAIL: 'Please enter valid email',
        PASSWORD_REQUIRED: 'Password is required'
      }
    },
    ECOMMERCE: {
      COMMON: {
        SELECTED_RECORDS_COUNT: 'Selected records count: ',
        ALL: 'All',
        SUSPENDED: 'Suspended',
        ACTIVE: 'Active',
        FILTER: 'Filter',
        BY_STATUS: 'by Status',
        BY_TYPE: 'by Type',
        BUSINESS: 'Business',
        INDIVIDUAL: 'Individual',
        SEARCH: 'Search',
        IN_ALL_FIELDS: 'in all fields'
      },
      ECOMMERCE: 'eCommerce',
      CUSTOMERS: {
        CUSTOMERS: 'Customers',
        CUSTOMERS_LIST: 'Customers list',
        NEW_CUSTOMER: 'New Customer',
        DELETE_CUSTOMER_SIMPLE: {
          TITLE: 'Customer Delete',
          DESCRIPTION: 'Are you sure to permanently delete this customer?',
          WAIT_DESCRIPTION: 'Customer is deleting...',
          MESSAGE: 'Customer has been deleted'
        },
        DELETE_CUSTOMER_MULTY: {
          TITLE: 'Customers Delete',
          DESCRIPTION: 'Are you sure to permanently delete selected customers?',
          WAIT_DESCRIPTION: 'Customers are deleting...',
          MESSAGE: 'Selected customers have been deleted'
        },
        UPDATE_STATUS: {
          TITLE: 'Status has been updated for selected customers',
          MESSAGE: 'Selected customers status have successfully been updated'
        },
        EDIT: {
          UPDATE_MESSAGE: 'Customer has been updated',
          ADD_MESSAGE: 'Customer has been created'
        }
      }
    },
    USER_MANAGEMENT: {
      TITLE: 'Manage Users',
      ADD_BUTTON: 'Add User',
      FILTER: {
        REGION: 'Region',
        MANAGER: 'Manager',
        TITLE: 'Title',
        APPLY_BTN: 'Apply',
        RESET_BTN: 'Reset',
        SELECT_PLACEHOLDER: 'Select',
        SEARCH: 'Search By Name, Username, Email',
        NO_MANAGER: 'No manager available'
      },
      LABEL: {
        ID: 'User ID',
        NAME: 'Name',
        USERNAME: 'Username',
        EMAIL: 'Email Address',
        TITLE: 'Title',
        MANAGER: 'Manager',
        REGION: 'Region Name',
        STATUS: 'Status',
        ACTION: 'Action',
        FIRSTNAME: 'First Name',
        LASTNAME: 'Last Name',
        SALESGROUP: 'Sales Group Number',
        ISS: 'ISS Number'
      },
      NO_USER: 'No User found',
      NO_SALE_GROUP: 'No Sales Group found',
      DELETE_USER: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this user?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'User has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting user. Please try again',
        UPDATE_STATUS: 'User status is updated successfully'
      },
      VALIDATION: {
        FIRSTNAME_REQUIRED: 'Please enter First name',
        LASTNAME_REQUIRED: 'Please enter Last name',
        EMAIL_REQUIRED: 'Please enter Email address',
        USERNAME_REQUIRED: 'Please enter Username',
        TITLE_REQUIRED: 'Please select Title',
        REGION_REQUIRED: 'Please select Region Name',
        MANAGER_REQUIRED: 'Please enter Manager',
        FIRSTNAME_INVALID: 'Please enter valid First name',
        LASTNAME_INVALID: 'Please enter valid Last name',
        EMAIL_INVALID: 'Please enter valid Email address',
        USERNAME_INVALID: 'Please enter valid Username',
        MANAGER_INVALID: 'Please enter valid Manager',
        SALESGROUP_REQUIRED: 'Please select Sales Group Number',
        ISS_NUMBER: 'Please enter ISS number',
        VALID_ISS: 'Please enter in number format'
      },
      CREATE_USER_SUCCESS: 'User has been created successfully',
      UPDATE_USER_SUCCESS: 'User has been updated successfully',
      ADD_USER_TITLE: 'Add User',
      EDIT_USER_TITLE: 'Edit User'
    },
    REGION: {
      TITLE: 'Regions',
      LABEL: {
        NAME: 'Name',
        MANAGER_NAME: 'Manager Name',
        ISS_NAME: 'ISS Name'
      },
      NO_REGION: 'No regions found',
      EDIT_REGION: 'Edit Region',
      ADD_REGION: 'Add Region',
      UPDATE_SUCCESS: 'Region has been updated successfully',
      ADD_SUCCESS: 'Region has been added successfully',
      VALIDATION: {
        ISS_NAME: 'ISS Name is required'
      }
    },
    NATIONAL_ACCOUNT: {
      TITLE: nationalAccount,
      LABEL: {
        NAME: 'NA Name',
        MANAGER_NAME: 'NA Manager Name',
        NUMBER: 'NA Number'
      },
      NO_ACCOUNT: 'No National Account found',
      VALIDATION: {
        NAME: 'NA Name is required',
        INVALID_NAME: 'Please enter name in valid format',
        MANAGER: 'NA Manager Name is required',
        NUMBER: 'NA Number is required',
        INVALID_NUMBER: 'Please enter in number format',
        INVALID_MIN_NAME: 'Please enter name in valid format with minimum 3 chars',
      },
      ADD_TITLE: 'Add National Account',
      EDIT_TITLE: 'Edit National Account',
      UPDATE_SUCCESS: 'National Account has been updated successfully',
      CREATE_SUCCESS: 'National Account has been added successfully',
      CLONE_SUCCESS: 'National Account has been cloned successfully',
      DELETE_ACCOUNT: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this national account?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'National Account has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting national account. Please try again'
      },
      FILTER: {
        SEARCH_BY_NAME: 'Search By NA Name',
        SEARCH_BY_NUM: 'Search By NA Number'
      }
    },
    SALES_GROUP: {
      TITLE: salesGroup,
      LABEL: {
        NAME: 'Name',
        REGION_NAME: 'Region Name',
        NUMBER: 'Sales Group Number'
      },
      VALIDATION: {
        NUMBER: 'Sales Group Number is required',
        INVALID_NUMBER: 'Enter Sales Group number in valid number format',
        REGION_NAME: 'Region Name is required'
      },
      NO_GROUP: 'No sales group found',
      ADD_TITLE: 'Add Sales Group',
      EDIT_TITLE: 'Edit Sales Group',
      UPDATE_SUCCESS: 'Sales Group has been updated successfully',
      CREATE_SUCCESS: 'Sales Group has been added successfully',
      CLONE_SUCCESS: 'Sales Group has been cloned successfully',
      DELETE_SALE_GROUP: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this sales group?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Sales Group has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting sales group. Please try again'
      },
    },
    AP_PP_CAPEX: {
      CAPEX_TAB: 'CAPEX',
      AP_PP_LIST: 'CAPEX > AIRplus & PAPERplus',
      CPA: 'CPA',
      IPA: 'IPA',
      PROPOSAL: 'PROPOSAL',
      DOCUMENTATION: 'Documentation',
      GENERAL_INFO: 'General Information',
      LABEL: {
        PRICE_GUARANTEE_TERM: 'Price Guarantee Term',
        SENT_APPROVAL_DATE: 'Sent for Approval Date',
        MONTHS: 'Months',
        END_NUMBER: 'Ship To (End User) Number',
        DISTRIBUTOR_NUMBER: 'Sold To (Distributor) Number',
        CS_END_NUMBER: 'Ship To',
        CS_DISTRIBUTOR_NUMBER: 'Sold To',
        STOROPACK_TM: "Storopack's T.M.",
        STOROPACK_FPS: "Storopack's FPS",
        STOROPACK_FPS_TM: "Storopack's FPS/TM",
        END_NAME: 'Ship To (End User) Name',
        DISTRIBUTOR_NAME: 'Sold To (Distributor) Name',
        CURRENT_SHIP: 'Current or New Ship To?',
        NATIONAL_ACCOUNT: nationalAccount,
        NA: 'NA #',
        NAM: 'NAM',
        NA_NAME: 'NA Name',
        GLOBAL_CAPEX: 'Global CAPEX',
        APPLY_PGT: 'Apply Price Guarantee Term',
        CAPEX_DETAIL: 'CAPEX Details',
        SALES_PERSON: 'Sales Person Name',
        TOTAL_AMOUNT: 'Total Amount',
        TOTAL_MARGIN_AMT: 'Total Margin Amount',
        MARGIN_PERCENT: 'Total Margin Percent'
      },
      SHIP_TYPE: {
        NEW: 'NEW',
        CURRENT: 'CURRENT'
      },
      VALIDATION: {
        MONTH: 'Please enter month',
        INVALID_MONTH: 'Enter number of months from 6 to 36. Enter 0 (zero) for no price hold.',
        INVALID_HOLD_MONTH: 'Enter number of months from 6 to 12. Enter 0 (zero) for no price hold.',
        CAPEX_DATE: 'Please enter approval date',
        END_NUMBER: 'Please enter Ship To number',
        DISTRIBUTOR_NUMBER: 'Please enter Sold To number',
        INVALID_END_NUMBER: 'Please enter valid Ship To number from 660000 to 759999',
        INVALID_DISTRIBUTOR_NUMBER: 'Please enter valid Sold To number from 600000 to 630000',
        INVALID_END_NUMBER_CANADA: 'Please enter valid Ship To number from 580000 to 589999',
        INVALID_DISTRIBUTOR_NUMBER_CANADA: 'Please enter valid Sold To number from 570000 to 579999',
        END_NAME: 'Please enter Ship To name',
        DISTRIBUTOR_NAME: 'Please enter Sold To name',
        INVALID_END_NAME: 'Please enter valid Ship To name',
        INVALID_DISTRIBUTOR_NAME: 'Please enter valid Sold To name',
        CURRENT_SHIP: 'Please select current or new ship to type',
        NA_NAM: 'NA Name is required',
        TM_REQUIRED: "Storopack's TM is required",
        FPS_REQUIRED: "Storopack's FPS is required",
        SHIP_NUMBER_INVALID: 'This is a National Accounts number.',
        CURRENCY: 'Please enter currency'
      },
      GENERAL_INFO_SUCCESS: 'CAPEX has been created successfully',
      GENERAL_INFO_UPDATE_SUCCESS: 'Data updated successfully',
      NON_EQUIPMENT: {
        TITLE: 'Non-Equipment Material',
        LABEL: {
          RAG: 'RYG',
          QTY_PER_MONTH: 'Qty Per Month',
          SALE_PRICE: 'Sale Price',
          BOOK_PRICE: bookPrice,
          TOTAL_SALE: 'Total Mo. Sale',
          MARGIN_PER_UNIT: 'Margin Per Unit',
          TOTAL_MARGIN: 'Total Mo. Margin',
          MATERIAL_GROUP: materialGroup,
          PLANT_NO: 'Plant No',
          MATERIAL_NUMBER: 'Material Number',
          DESC: materialDesc,
          UNIT: 'Unit',
          MONTH_SALE: 'Total Month Sale',
          MONTH_MARGIN: 'Total Month Margin',
          MARGIN_PERCENT: 'Margin %'
        },
        NO_DATA: 'No Non-Equipment Material Found',
        TABLE_TEXT: 'Mate = Material, Mach = Machine, Qty = Quantity, Mo = Month',
        ADD_TITLE: 'Add New',
        VALIDATION: {
          PLANT_NO: 'Plant No is required',
          NUMBER: 'Material Number is required',
          DESC: 'Description is required',
          UNITS: 'Units is required',
          QTY_PER_MONTH: 'Qty Per Month is required',
          BOOK_PRICE: 'Book Price is required',
          SALE_PRICE: "Sale Price is required",
          MATERIAL_GROUP: 'Material Group is required',
          INVALID_SALE_PRICE: 'Please enter Sale Price in valid number format',
          INVALID_QTY: 'Please enter Qty per month in valid number format',
          INVALID_BOOK_PRICE: 'Please enter Book Price in valid number format'
        }
      },
      MATERIAL: {
        TITLE: 'AIRplus & PAPERplus Material',
        LABEL: {
          MATERIAL_SUMMARY: 'Material Summary',
          RAG: 'RYG',
          TOTAL_MONTHLY_SALES: 'Total Material Monthly Sales',
          TOTAL_MONTHLY_MARGIN: 'Total Material Monthly Margin',
          MATERIAL_MARGIN_PERCENT: 'Material Margin Percent',
          TOTAL_MARGIN_PERCENT: 'Total Margin Percent',
          MATERIAL_TYPE: materialCategory,
          MATERIAL_NUMBER: 'Material Number',
          DESC: materialDesc,
          PROPERTIES: 'Material Properties',
          AVG_ROLLS: 'Avg. Rolls Sold/Mo.',
          ROLL_PER_PALLET: 'Rolls/Bundles per pallet',
          PALLETS_MO: 'Pallets/Mo.',
          BOOK_PRICE: bookPrice,
          FINAL_PRICE: "100' Final Mate. Price",
          ROLL_PRICE: 'Roll Price',
          TOTAL_MONTHLY_SALE: 'Total Mthly Sales',
          MARGIN_PER_FT: 'Margin per 100ft',
          TOTAL_MARGIN: 'Total Monthly Margin',
          NON_STANDARD: 'Non Standard',
          PALLETS_MONTH: 'Pallets/Month',
          FINAL_MAT_PRICE: "100' Final Material Price",
          MATERIAL_GROUP: materialGroup,
          UNIT: 'Unit'
        },
        NO_DATA: 'No AIRplus & PAPERplus Material Found',
        DELETE_SUCCESS: 'AIRplus & PAPERplus Material has been deleted successfully',
        DELETE_BODY: 'Are you sure you want to delete this AIRplus & PAPERplus material?',
        TABLE_TEXT: 'Mate = Material, Mach = Machine, Qty = Quantity, Mo = Month, Mthly = Monthly',
        ADD_TITLE: 'Add New',
        VALIDATION: {
          NUMBER: 'Material Number is required',
          DESC: 'Material Description is required',
          MATERIAL_GROUP: 'Please enter material group',
          AVG_ROLLS: 'Avg. Rolls Sold/Mo is required',
          ROLLS_PER_PALLETS: 'Rolls/Bundles per pallet is required',
          PALLET_MONTH: 'Pallets/Month is required',
          BOOK_PRICE: 'Book Price is required',
          FINAL_PRICE: "100' Final Material Price is required",
          MNTHLY_SALE: 'Total Mthly Sales is required',
          MARGIN_FT: 'Margin per 100ft is required',
          MNTHLY_MARGIN: 'Total Mthly Margin is required',
          MATERIAL_TYPE: materialCategoryRequired,
          ROLL_PRICE: 'Roll Price is required',
          INVALID_AVG_ROLLS: 'Please enter Avg. Rolls Sold/Mo in valid number format',
          INVALID_ROLLS_PER_PALLETS: 'Please enter Rolls/Bundles per pallet in valid number format',
          INVALID_PALLET_MONTH: 'Please enter Pallets/Month in valid number format',
          INVALID_BOOK_PRICE: 'Please enter Book Price in valid number format',
          INVALID_FINAL_PRICE: "Please enter 100' Final Material Price in valid number format",
          INVALID_AVG_YEAR: 'Please enter Average Unit Sold/Year in valid number format',
          INVALID_PRICE_PER_UNIT: 'Please enter price/unit in valid number format',
          PRICE_PER_UNIT: 'Price/Unit is required',
          AVG_YEAR: 'Average Unit Sold/Year is required'
        }
      },
      EQUIPMENT: {
        TITLE: 'Equipment',
        LABEL: {
          RAG: 'RYG',
          MACHINE_REQD: 'Total Machines Required',
          MACHINE_COST_PER_MONTH: 'Total Machine Cost / Month',
          TOTAL_COST_TO_TERRITORY: 'Total Monthly Cost to Territory',
          MACHINE_TYPE: materialCategory,
          MACHINE_NUMBER: 'Machine Number',
          DESC: 'Machine Description',
          MACHINE_QTY: 'Mach. Qty',
          RENT_CHARGE: 'Rent Charge Each',
          MATERIAL_USED: 'Material Used',
          LEASE_REV: 'Lease Rev/Mo.',
          MACHINE_COST: 'Machine Cost/Month',
          COST_TO_TERRITORY: 'Mthly Cost to Territory',
          MATE_REV: "Mate/100' Rev/Mach.",
          MARGIN_MACHINE: "Margin/Mach.+Mate/100'",
          QUANTITY: 'Machine Quantity',
          LEASE: 'Lease Revenue/Month',
          MONTH_COST_TO_TERRITORY: monthlyCostToTerritory,
          MATE: "Material/100' Revenue/Machine",
          MARGIN_MAT: "Margin/Machine+Material/100'",
          ALL_LISTED: 'All Materials Listed'
        },
        NO_DATA: 'No Equipment Found',
        TABLE_TEXT: 'Mate = Material, Mach = Machine, Qty = Quantity, Mo = Month, Rev = Revenue',
        ADD_TITLE: 'Add New',
        VALIDATION: {
          NUMBER: 'Machine Number is required',
          DESC: 'Material Description is required',
          QUANTITY: 'Quantity is required',
          RENT_CHARGE: 'Rent Charge Each is required',
          MATERIAL_USED: 'Material Used is required',
          MAT_USED_VALIDATION: "AP/PP Material Group doesn't match with Equipment Group. Please select another Machine Number & Description.",
          MACHINE_TYPE: materialCategoryRequired,
          INVALID_QUANTITY: 'Please enter Quantity in valid number format',
          INVALID_RENT_CHARGE: 'Please enter Rent Charge in valid number format',
          NO_MATERIAL: 'Please enter AIRplus/PAPERplus material before adding equipment'
        }
      },
      INTEGRATION: {
        TITLE: integrationSystem,
        LABEL: {
          RAG: 'RYG',
          INTEGRATION_REQD: 'Total Integrations Required',
          TOTAL_INTEGRATION_COST: 'Total Integration Cost',
          TOTAL_SALE_CHARGED: 'Total Sale Charged',
          TOTAL_COST_TO_TERRITORY: totalCostToTerritory,
          MONTHLY_COST: monthlyCostToTerritory,
          SYSTEM_TYPE: materialCategory,
          SYSTEM_NUMBER: 'System Number',
          DESC: 'System Description',
          SYSTEM_QTY: 'Sys. Qty',
          SALE_PRICE: 'Sale/Lease Price Each',
          MACHINE_INSTALLED: 'Machine installed with System',
          SYS_COST: 'System Cost(If Custom)',
          STD_SYS_COST: 'Std. Sys. Total Cost',
          STD_CUSTOM_COST: 'Std. Custom Total Cost',
          STD_CUSTOM: 'Standard Custom Total Cost',
          ADD_CUSTOM: 'Add Custom Integration',
          COST_TO_TERRITORY: totalCostToTerritory,
          MATE_REV: "Mate./100' Rev./Machine/Integ.",
          MARGIN_MACHINE: "Margin of Integ.+Mach.+Mate/100'",
          QUANTITY: 'System Quantity',
          STD_COST: 'Standard System Total Cost',
          MATE: "Material/100' Revenue/Machine/Integra..",
          MARGIN_MATE: "Margin of Integ.+Machine+Material/100'",
          ALL_MACHINE_INSTALLED: 'All Machine Installed'
        },
        NO_DATA: 'No Integration System Found',
        TABLE_TEXT: 'Mate = Material, Mach = Machine, Qty = Quantity, Mo = Month, Integ. = Integration,  Rev = Revenue, Std = Standard, Sys = System',
        ADD_TITLE: 'Add New',
        VALIDATION: {
          NUMBER: 'System Number is required',
          DESC: 'System Description is required',
          QUANTITY: 'System Quantity is required',
          SALE_PRICE: 'Sale Price Each is required',
          MATERIAL_INSTALLED: 'Machine Installed with system is required',
          SYS_CUSTOM_COST: 'System Cost is required',
          SYSTEM_TYPE: materialCategoryRequired,
          INVALID_QUANTITY: 'Please enter System Quantity in valid number format',
          INVALID_SALE_PRICE: 'Please enter Sale Price in valid number format',
          INVALID_SYS_COST: 'Please enter System Cost in valid number format',
          NO_EQUIPMENT: 'Please enter Equipment before adding integration system'
        }
      },
      SUMMARY: {
        TITLE: 'Summary',
        DOWNLOAD: 'Freight Policy file has been downloaded successfully',
        CATALOG_DOWNLOAD: 'Integration Catalog File has been downloaded successfully',
        LABEL: {
          TOTAL_MONTHLY_SALE: 'Total Material Monthly Sales',
          TOTAL_MONTHLY_MARGIN: 'Total Monthly Margin $ After Equipment/Integration',
          TOTAL_MONTHLY_MARGIN_P: 'Total Monthly Margin % After Equipment/Integrations',
          FREIGHT_CHARGE: 'Freight Charge',
          FREIGHT_POLICY: 'Freight Policy',
          UNIT: 'Unit',
          CHARGE_TO: 'Charge To',
          BOX_NOTE: 'CAPEX, Around-the-Box Notes & Possible Solutions',
          FREIGHT_INSTRUCTION: 'Freight Instructions',
          LOCAL_FREIGHT_POLICY: 'per Corporate Approved Freight Policy'
        },
        SUBMIT: 'Save & Submit For Approval',
        UPDATE_SUCCESS: 'Data updated successfully',
        VALIDATION: {
          FREIGHT_CHARGE: 'Freight Charge is required',
          UNIT: 'Unit is required',
          CHARGE_TO: 'Charge To is required',
          INVALID_CHARGE: 'Enter Freight charge in a valid format',
          INVALID_NOTE: 'Enter note in valid format of maximum 255 characters',
          INVALID_INSTRUCTION: 'Enter freight instruction in valid format of maximum 255 characters',
          INSTRUCTION: 'Freight Instructions is required'
        }
      }
    },
    FREIGHT_TABLE: {
      TITLE: 'Freight Verbiage',
      CATALOG_TITLE: 'Integration Catalog',
      LABEL: {
        ID: 'ID',
        FILE: 'Filename',
        DATE: 'Date'
      },
      SUCCESS: 'Freight Verbiage File has been updated successfully',
      CATALOG_SUCCESS: 'Integration Catalog File has been updated successfully'
    },
    DOCUMENT: {
      TABLE: {
        DOC_ID: '# Doc',
        DOC_GROUP: 'Document Group',
        NAME: 'Document(s) Name',
        CREATED: 'Modified By',
        DATE: 'Modified On',
        ACTION: 'Actions'
      },
      DELETE_GROUP: {
        TITLE: 'Delete Document Group',
        BODY: 'It will delete all the uploaded documents of the group. Are you sure you want to delete document group? ',
        SUCCESS: 'Document Group has been deleted successfully'
      },
      DELETE_DOC: {
        TITLE: 'Delete Document',
        BODY: 'Are you sure you want to delete document? ',
        SUCCESS: 'Document has been deleted successfully'
      },
      ADD_DOC: {
        TITLE: 'Document',
        LABEL: {
          NAME: 'Document Group',
          CHOOSE_FILE: 'Choose document to upload:',
          DOCUMENTS: 'Document List:'
        },
        VALIDATION: {
          GROUP: 'Document Group is required',
          INVALID_GRP: 'Please enter in valid format'
        }
      },
      ADD_SUCCESS: 'Document Group has been added successfully',
      UPDATE_SUCCESS: 'Document Group has been updated successfully',
      DOWNLOAD_SUCCESS: 'Document has been downloaded successfully'
    },
    FP_SUMMARY: {
      SYSTEM_MONTH: 'Per System/Month',
      LEASE_OPTION: 'Lease Option',
      REVENUE: 'Revenue',
      MARGIN: 'Margin',
      MARGIN_OVERVIEW: 'Margin Overview',
      GTM_P: 'GTM %',
      GTM_D: 'GTM $',
      MATERIAL_FILM: 'Material & Film',
      SYS_LEASE: 'System Lease/Service'
    },
    FP_CAPEX: {
      FP_LIST: 'CAPEX > FOAMplus',
      MATERIAL: {
        TITLE: 'FOAMplus Material',
        LABEL: {
          MATERIAL_ID: 'Material #',
          DESC: materialDesc,
          UNIT: 'Unit',
          PRICE_PER_UNIT: 'Price/Unit',
          RAG: 'RYG',
          AVG_SOLD_YEAR: 'Avg. Un. Sold/Year',
          AVG_SOLD_MONTH: 'Avg. Un./Month',
          SALES_MONTH: 'Sales/Month',
          COST_MONTH: 'Cost/Month',
          GTM: 'GTM',
          GTMP: 'GTM %',
          TOTALS: 'TOTALS',
          SOLD_YEAR: 'Average Unit Sold/Year',
          SOLD_MONTH: 'Average Unit/Month'
        },
        DELETE_SUCCESS: 'FOAMplus Material has been deleted successfully',
        DELETE_BODY: 'Are you sure you want to delete this FOAMplus material?',
        NO_FP_MATERIAL: 'No FOAMplus Material found'
      },
      EQUIPMENT: {
        LABEL: {
          RAG: 'RYG',
          TOTAL_MACHINE_REQD: 'Total Machine Required',
          TOTAL_SALE_CHARGED: 'Total Sale Charged',
          TOTAL_COST_TO_TERRITORY: totalCostToTerritory,
          MONTHLY_TERRITORY_COST: `${monthlyCostToTerritory} over 12 Months`,
          MONTHLY_COST_TERRITORY: 'Monthly Cost to Territory',
          MACHINE: 'Machine #',
          DESC: 'Description',
          MACHINE_QTY: 'Machine Quantity',
          LEASE_CHARGE: 'Lease Charge/Month',
          CHARGE_LOST: 'Charge for Damage or Lost',
          LEASE_OWN_MONTH: 'Lease to Own Months Allocated',
          TOTAL_SALE_PRICE: 'Machine Total Sale Price',
          SERIAL_NO: 'Machine Serial Number',
          END_USER: 'End-User or Distributor',
          LEASE_COST: 'Machine Lease Cost',
          LEASE_REVENUE: 'Lease Revenue/Month',
          MONTHLY_COST_TO_TERRITORY: monthlyCostToTerritory,
          ANNUAL_SERVICE_CHARGE: 'Annual Service Agreement Charge'
        },
        DELETE_SUCCESS: 'FOAMplus Equipment has been deleted successfully',
        DELETE_BODY: 'Are you sure you want to delete this FOAMplus equipment?',
        NO_FP_EQUIPMENT: 'No FOAMplus Equipment found',
        VALIDATION: {
          MACHINE_NO: 'Machine Number is required',
          DESC: 'Description is required',
          MACHINE_QTY: 'Machine Quantity is required',
          INVALID_MACHINE_QTY: 'Please enter machine quantity in valid format',
          LEASE_CHARGE: 'Lease Charge/Month is required',
          INVALID_LEASE_CHARGE: 'Please enter lease charge/month in valid format',
          LOST_CHARGE: 'Charge for Damage or Lost is required',
          INVALID_LOST_CHARGE: 'Please enter charge for damage or lost in valid format',
          LEASE_ALLOCATED: 'Lease to Own Months Allocated is required',
          INVALID_LEASE_ALLOCATED: 'Please enter lease to own months allocated in valid format',
          TOTAL_SALE_PRICE: 'Machine Total Sale Price is required',
          INVALID_TOTAL_SALE_PRICE: 'Please enter machine total sale price in valid format',
          SERIAL_NO: 'Machine Serial Number is required',
          INVALID_SERIAL_NO: 'Please enter machine serial number in valid format',
          END_USER: 'End-User or Distributor is required'
        }
      }
    },
    NON_EQUIPMENT: {
      TITLE: 'Non-Equipment > Material',
      FILTER: {
        MATERIAL_GROUP: materialGroup,
        SELECT_PLACEHOLDER: 'Select',
        SEARCH_BY_PLANT_NO: 'Search by Plant No',
        SEARCH_BY_NUMBER: 'Search by Number',
        SEARCH_BY_DESCRIPTION: 'Search by Description',
        APPLY_BTN: 'Apply',
        RESET_BTN: 'Reset'
      },
      LABEL: {
        MATERIAL_CATEGORY: materialCategory,
        MATERIAL_GROUP: materialGroup,
        PLANT_NO: 'Plant No',
        NUMBER: 'Number',
        DESCRIPTION: 'Description',
        CUBIC_FOOT: 'Cubic Foot',
        UNITS: 'Unit',
        COST: 'Cost Per Unit',
        BOOK_PRICE: bookPrice,
        ACTION: 'Action'
      },
      VALIDATION: {
        MATERIAL_CATEGORY: 'Please select Material Category',
        MATERIAL_GROUP: 'Please select Material Group',
        PLANT_NO: 'Please select Plant No',
        NUMBER: 'Please enter number',
        INVALID_NUMBER: invalidNumber,
        DESCRIPTION: descRequired,
        INVALID_DESCRPTION: 'Please enter valid description',
        CUBIC_FOOT: 'Please enter Cubic Foot',
        INVALID_CUBIC_FOOT: 'Please enter cubic foot in valid format',
        UNITS: 'Please select Unit',
        COST: 'Please enter Cost Per Unit',
        BOOK_PRICE: 'Please enter book price',
        INVALID_COST: 'Please enter cost in valid format',
        INVALID_BOOK_PRICE: 'Please enter book price in valid format',
      },
      NO_DATA: 'No Non-Equipment Material found',
      CREATE_SUCCESS: 'Non-Equipment Material has been created successfully',
      UPDATE_SUCCESS: 'Non-Equipment Material has been updated successfully',
      CLONE_SUCCESS: 'Non-Equipment Material has been cloned successfully',
      EDIT_TITLE: 'Edit Material',
      ADD_TITLE: addMaterial,
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this non-equipment material?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Non-Equipment Material has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting non-equipment material. Please try again'
      },
    },
    MATERIAL: {
      TITLE: 'AIRplus & PAPERplus > Material',
      FP_TITLE: 'FOAMplus > Material',
      ADD_BUTTON: 'Add New',
      FILTER: {
        MATERIAL_CATEGORY: materialCategory,
        MATERIAL_GROUP: materialGroup,
        SELECT_PLACEHOLDER: 'Select',
        MATERIAL_PROPERTY: 'Material Property',
        SEARCH_BY_MATERIAL_NO: 'Search by Number',
        SEARCH_BY_DESC: 'Search by Description',
      },
      LABEL: {
        MATERIAL_CATEGORY: materialCategory,
        NUMBER: 'Number',
        DESCRIPTION: 'Description',
        MATERIAL_GROUP: materialGroup,
        UNIT: 'Unit',
        PRICE_PER_LB: "Cost per Unit",
        COST_PER_100: "Cost per 100'",
        FEET_MATERIAL_PRICE: "100' Feet Final Material Price",
        COST_PER_ROLL: 'Cost per Roll',
        ACTION: 'Action',
        NON_STANDARD: 'Non Standard',
        STANDARD: 'Standard',
        MATERIAL_PROPERTY: 'Material Properties',
        UNITS: 'Units',
        LINEAR_FEET_ROLL: 'Linear Feet Roll',
        ROLL_PER_PALLET: 'Rolls / Pallet',
        HUNDRED_PER_ROLL: "100' / Roll",
        COST_100: "Cost Per 100' (with royalty)",
        ROYALTY_RATE: "Royalty Rate 100'",
        TRANSFER_COST: 'Transfer Cost',
        ROLL_WIDTH: 'Roll Width',
        CUSTOM: 'Type'
      },
      NO_DATA: 'No Material found',
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this material?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Material has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting material. Please try again'
      },
      ADD_TITLE: addMaterial,
      EDIT_TITLE: 'Edit Material',
      NO_UNIT: 'No Units found',
      VALIDATION: {
        MATERIAL_CATEGORY: 'Please select Material Category',
        MATERIAL_GROUP: 'Please select Material Group',
        MATERIAL_PROPERTY: 'Please select Material Property',
        NUMBER: 'Please enter number',
        INVALID_NUMBER: invalidNumber,
        DESCRIPTION: descRequired,
        INVALID_DESCRPTION: 'Please enter valid description',
        UNIT: 'Please select units',
        LINEAR_FEET_ROLL: 'Please enter linear feet roll',
        INVALID_LINEAR_FEET_ROLL: 'Please enter linear feet roll in valid format',
        ROLL_PER_PALLET: 'Please enter rolls / pallet',
        INVALID_ROLL_PER_PALLET: 'Please enter rolls / pallet in valid format',
        HUNDRED_PER_ROLL: "Please enter 100'/Roll",
        INVALID_HUNDRED_PER_ROLL: "Please enter 100'/Roll in valid format",
        COST_100: "Please enter cost per 100'",
        INVALID_COST_100: "Please enter cost per 100' in valid format",
        ROYALTY_RATE: "Please enter royalty rate 100'",
        INVALID_ROYALTY_RATE: "Please enter royalty rate 100' in valid format",
        TRANSFER_COST: 'Please enter transfer cost',
        INVALID_TRANSFER_COST: 'Please enter transfer cost in valid format',
        COST_PER_ROLL: 'Please enter cost per roll',
        INVALID_COST_PER_ROLL: 'Please enter cost per roll in valid format',
        ROLL_WIDTH: 'Please enter roll width',
        INVALID_ROLL_WIDTH: 'Please enter roll width in valid format',
        PRICE_PER_LB: 'Please enter cost per unit',
        INVALID_PRICE_PER_LB: 'Please enter cost per unit in valid format'
      },
      CREATE_SUCCESS: 'Material has been created successfully',
      UPDATE_SUCCESS: 'Material has been updated successfully',
      CLONE_SUCCESS: 'Material has been cloned successfully',
    },
    EQUIPMENT: {
      FP_TITLE: 'FOAMplus > Equipment',
      AP_PP_TITLE: 'AIRplus & PAPERplus > Equipment',
      ADD_BUTTON: 'Add New',
      FILTER: {
        MATERIAL_GROUP: materialGroup,
        APPLY_BTN: 'Apply',
        RESET_BTN: 'Reset',
        SELECT_PLACEHOLDER: 'Select',
        SEARCH_BY_MACHINE_NUMBER: 'Search By Number',
        SEARCH_BY_DESCRIPTION: 'Search By Description'
      },
      LABEL: {
        MATERIAL_CATEGORY: materialCategory,
        NUMBER: 'Number',
        DESCRIPTION: 'Description',
        MONTHLY_RENT: 'Monthly Rent',
        MATERIAL_GROUP: materialGroup,
        ACTION: 'Action'
      },
      NO_DATA: 'No Equipment found',
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this equipment?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Equipment has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting equipment. Please try again'
      },
      VALIDATION: {
        NUMBER_REQUIRED: 'Please enter machine number',
        DESCRIPTION_REQUIRED: descRequired,
        MONTHLY_RENT_REQUIRED: 'Please enter monthly rent',
        MATERIAL_GROUP_REQUIRED: 'Please select material group',
        MATERIAL_CATEGORY_REQUIRED: selectCategory,
        MONTHLY_RENT_INVALID: 'Please enter valid amount',
        NUMBER_INVALID: invalidNumber,
      },
      CREATE_SUCCESS: 'Equipment has been created successfully',
      UPDATE_SUCCESS: 'Equipment has been updated successfully',
      CLONE_SUCCESS: 'Equipment has been cloned successfully',
      ADD_TITLE: 'Add Equipment',
      EDIT_TITLE: 'Edit Equipment'
    },
    INTEGRATION_SYSTEM: {
      TITLE: 'AIRplus & PAPERplus > Integration System',
      ADD_BUTTON: 'Add New',
      FILTER: {
        MATERIAL_CATEGORY: materialCategory,
        APPLY_BTN: 'Apply',
        RESET_BTN: 'Reset',
        SELECT_PLACEHOLDER: 'Select',
        SEARCH_BY_SYSTEM_NUMBER: 'Search By Number',
        SEARCH_BY_DESCRIPTION: 'Search By Description'
      },
      LABEL: {
        MATERIAL_CATEGORY: materialCategory,
        NUMBER: 'Number',
        DESCRIPTION: 'Description',
        LEASE_COST: 'Lease Cost',
        ACTION: 'Action'
      },
      NO_DATA: 'No Integration System found',
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this integration system?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Integration system has been deleted successfully',
        ERROR_MSG: 'There was some issue while deleting integration system. Please try again'
      },
      VALIDATION: {
        NUMBER_REQUIRED: 'Please enter integration system number',
        DESCRIPTION_REQUIRED: descRequired,
        LEASE_COST_REQUIRED: 'Please enter lease cost',
        MATERIAL_CATEGORY_REQUIRED: selectCategory,
        LEASE_COST_INVALID: 'Please enter valid amount',
        NUMBER_INVALID: 'Please enter number in valid format'
      },
      CREATE_SUCCESS: 'Integration system has been created successfully',
      UPDATE_SUCCESS: 'Integration system has been updated successfully',
      CLONE_SUCCESS: 'Integration system has been cloned successfully',
      ADD_TITLE: 'Add Integration System',
      EDIT_TITLE: 'Edit Integration System'
    },
    MATERIAL_CATEGORY: {
      TITLE: 'General > Material Category',
      ADD_BUTTON: 'Add New',
      LABEL: {
        NAME: materialCategory,
        RED: 'Red (Margin)',
        YELLOW: 'Yellow (Margin)',
        ACTION: 'Action'
      },
      NO_DATA: 'No Material Category found',
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this material category?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Material category has been delete successfully',
        ERROR_MSG: 'There was some issue while deleting material category. Please try again'
      },
      VALIDATION: {
        NAME_REQUIRED: 'Please enter material category name',
        MARGIN: 'Please enter Margin',
        MARGIN_VALIDATE: 'Yellow (Margin) should be greater than Red (Margin)'
      },
      CREATE_SUCCESS: 'Material category has been created successfully',
      UPDATE_SUCCESS: 'Material category has been updated successfully',
      CLONE_SUCCESS: 'Material category has been cloned successfully',
      ADD_TITLE: 'Add Material category',
      EDIT_TITLE: 'Edit Material category'
    },
    MATERIAL_GROUP: {
      TITLE: 'General > Material Group',
      ADD_BUTTON: 'Add New',
      LABEL: {
        NAME: materialGroup,
        MATERIAL_CATEGORY: materialCategory,
        ROYALTY_RATE_TYPE: 'Royalty Rate Type',
        ACTION: 'Action'
      },
      NO_DATA: 'No Material Group found',
      DELETE_DIALOG: {
        TITLE: confirmDelete,
        DESC: 'Are you sure you want to delete this material group?',
        DELETE_BTN: 'Delete',
        CANCEL_BTN: 'Cancel',
        SUCCESS_MSG: 'Material group has been delete successfully',
        ERROR_MSG: 'There was some issue while deleting material group. Please try again'
      },
      VALIDATION: {
        NAME_REQUIRED: 'Please enter material group name',
        MATERIAL_CATEGORY_REQUIRED: selectCategory,
        ROYALTY_RATE_TYPE_REQUIRED: 'Please select royalty rate type',
        VALID_NAME: 'Please enter valid name with minimum 3 chars'
      },
      CREATE_SUCCESS: 'Material group has been created successfully',
      UPDATE_SUCCESS: 'Material group has been updated successfully',
      CLONE_SUCCESS: 'Material group has been cloned successfully',
      ADD_TITLE: 'Add Material group',
      EDIT_TITLE: 'Edit Material group'
    }
  }
};
