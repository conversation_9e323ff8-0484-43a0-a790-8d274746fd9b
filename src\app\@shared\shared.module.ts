/**
 * @sunflowerlab
 * <AUTHOR>
 */
import { CommonModule } from "@angular/common";
import { RoundPipe, NumberWithCommasPipe, RemoveCommaPipe } from "./pipes";
import { FormsModule } from "@angular/forms";
import { HTTP_INTERCEPTORS } from "@angular/common/http";
import { InterceptorService, KtDialogService } from "./services";
import { NgModule } from "@angular/core";
import { ContentAnimateDirective } from "./directives/content-animate.directive";
import { HeaderDirective } from "./directives/header.directive";
import { MenuDirective } from "./directives/menu.directive";
import { OffcanvasDirective } from "./directives/offcanvas.directive";
import { ScrollTopDirective } from "./directives/scroll-top.directive";
import { StickyDirective } from "./directives/sticky.directive";
import { TabClickEventDirective } from "./directives/tab-click-event.directive";
import { ToggleDirective } from "./directives/toggle.directive";
import { FirstLetterPipe } from "./pipes/first-letter.pipe";
import { GetObjectPipe } from "./pipes/get-object.pipe";
import { JoinPipe } from "./pipes/join.pipe";
import { SafePipe } from "./pipes/safe.pipe";
import { TimeElapsedPipe } from "./pipes/time-elapsed.pipe";
import { LayoutConfigService } from "./services/layout-config.service";
import { MenuConfigService } from "./services/menu-config.service";
import { MenuAsideService } from "./services/menu-aside.service";
import { PageConfigService } from "./services/page-config.service";
import { SplashScreenService } from "./services/splash-screen.service";
import { SubheaderService } from "./services/subheader.service";
import { LayoutRefService } from "./services/layout-ref.service";
import { LayoutUtilsService } from "./services/layout-utils.service";
import { TranslateModule } from "@ngx-translate/core";
import { SflBaseComponent } from "./components/sfl-base/sfl-base.component";
import { SflSortByDirective, SflSortDirective, PasswordEyeDirective, SflIsFetchingDirective, SflShowSpinnerDirective, EmailCheckValidator, RequiredNoSpaceValidator } from '@shared/directives';
import { SflLoadingComponent } from './components/sfl-loading/sfl-loading.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FontAwesomeModule, FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faArrowCircleDown, faArrowCircleUp, faArrowRight, faCheckCircle, faPrint, fas, faSignature, faSort, faSortDown, faSortUp, faSpinner, faTimes, faTrashAlt, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { NgbModule, NgbProgressbarModule } from '@ng-bootstrap/ng-bootstrap';
import { ShowPaginationElementsComponent } from './components/show-pagination-elements/show-pagination-elements.component';
import { FpMaterialExportComponent } from './templates/fp-material-export/fp-material-export.component';
import { ExportAsModule } from 'ngx-export-as';
import { MaterialGroupExportComponent } from './templates/material-group-export/material-group-export.component';
import { MaterialExportComponent } from './templates/material-export/material-export.component';
import { IntegrationSystemExportComponent } from './templates/integration-system-export/integration-system-export.component';
import { NonEquipmentExportComponent } from './templates/non-equipment-export/non-equipment-export.component';
import { NationalAccountExportComponent } from './templates/national-account-export/national-account-export.component';
import { RegionExportComponent } from './templates/region-export/region-export.component';
import { SalesGroupExportComponent } from './templates/sales-group-export/sales-group-export.component';
import { EquipmentExportComponent } from './templates/equipment-export/equipment-export.component';
import { SampleExportComponent } from './templates/sample-export/sample-export.component';
import { MaterialCategoryComponent } from './templates/material-category/material-category.component';
import { UserExportComponent } from './templates/user-export/user-export.component';
import { ReplaceSymbolPipe } from './pipes/replace-symbol.pipe';
import { AutoLogoutComponent } from './components/auto-logout/auto-logout.component';
import { UnitExportComponent } from './templates/unit-export/unit-export.component';
import { PlantExportComponent } from './templates/plant-export/plant-export.component';
import { AppLoaderComponent } from "./components/app-loader/app-loader.component";

const COMPONENTS = [
  AppLoaderComponent
];
const PIPES = [
  RoundPipe,
  NumberWithCommasPipe,
  FirstLetterPipe,
  GetObjectPipe,
  JoinPipe,
  SafePipe,
  TimeElapsedPipe,
  ReplaceSymbolPipe,
  RemoveCommaPipe
];
const DIRECTIVES = [
  ContentAnimateDirective,
  HeaderDirective,
  MenuDirective,
  OffcanvasDirective,
  ScrollTopDirective,
  StickyDirective,
  TabClickEventDirective,
  ToggleDirective,
  SflSortByDirective,
  SflSortDirective,
  PasswordEyeDirective,
  SflIsFetchingDirective,
  SflShowSpinnerDirective,
  EmailCheckValidator,
  RequiredNoSpaceValidator
];

const MODULES = [
  MatProgressSpinnerModule,
  FontAwesomeModule,
  NgbModule,
  ExportAsModule,
  NgbProgressbarModule
]

@NgModule({
  imports: [CommonModule, FormsModule, TranslateModule.forChild(), ...MODULES],
  exports: [
    CommonModule,
    FormsModule,
    ...PIPES,
    ...COMPONENTS,
    ...DIRECTIVES,
    TranslateModule,
    ...MODULES,
    ShowPaginationElementsComponent,
    FpMaterialExportComponent,
    MaterialGroupExportComponent,
    MaterialExportComponent,
    IntegrationSystemExportComponent,
    NonEquipmentExportComponent,
    NationalAccountExportComponent,
    SalesGroupExportComponent,
    RegionExportComponent,
    EquipmentExportComponent,
    SampleExportComponent,
    MaterialCategoryComponent,
    UserExportComponent,
    AutoLogoutComponent,
    UnitExportComponent,
    PlantExportComponent
  ],
  declarations: [...COMPONENTS, ...PIPES, ...DIRECTIVES, SflBaseComponent, SflLoadingComponent, ShowPaginationElementsComponent, FpMaterialExportComponent, MaterialGroupExportComponent, MaterialExportComponent, IntegrationSystemExportComponent, NonEquipmentExportComponent, NationalAccountExportComponent, RegionExportComponent, SalesGroupExportComponent, EquipmentExportComponent, SampleExportComponent, MaterialCategoryComponent, UserExportComponent, AutoLogoutComponent, UnitExportComponent, PlantExportComponent],
  providers: [
    KtDialogService,
    LayoutConfigService,
    MenuConfigService,
    MenuAsideService,
    PageConfigService,
    SplashScreenService,
    SubheaderService,
    LayoutRefService,
    LayoutUtilsService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
    },
  ],
})
export class SharedModule {
  constructor(readonly faLibrary: FaIconLibrary) {
    faLibrary.addIconPacks(fas);
    faLibrary.addIcons(faSort, faSortUp, faSortDown, faPrint, faSignature, faArrowCircleDown, faArrowCircleUp, faCheckCircle, faTimes, faSpinner, faArrowRight, faTrashAlt, faExclamationTriangle);
  }
 }
