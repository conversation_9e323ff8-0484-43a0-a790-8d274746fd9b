import { Pageable } from '@shared/services/utils';

export class RegisterToken {
  recipientId: string;
  token: string;
}

export class SubscribeTopic {
  registerRecipientWithTokenRequest: RegisterToken[] = [];
  topic: string;
}

export class UnsubscribeTopic {
  unregisterRecipientWithTokenRequest: RegisterToken[] = [];
  topic: string;
}

export class Notification {
  body: string;
  id: string;
  jsonReferenceData: {};
  readStatus: string;
  recipientId: string;
  sentDate: string;
  subject: string;
}

export class NotificationList extends Pageable {
  content: Notification[] = [];
}

export class NotificationFilter {
  endDateTime: string;
  recipientId: string;
  startDateTime: string;
  readStatus: string;
}

export class NotificationCount {
  unreadCount: number;
}
