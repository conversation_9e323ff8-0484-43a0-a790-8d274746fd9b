<kt-portlet>
  <mat-accordion>
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title class="header-title">
          {{'IPA.CUSTOMER_LAYOUT.TITLE' | translate}}
        </mat-panel-title>
        <mat-panel-description *ngIf="panelOpenState" (click)="$event.stopPropagation();" class="ml-4">
          <button mat-raised-button *ngIf="this.title !== this.titleList.VIEWER_C1" color="primary" class="btn btn-primary add-btn ml-2" (click)="submit()" [isSubmitting]="isSubmitting"
            [disabled]="isSubmitting || ipaStatus !== status.OPEN || disableAdd || isAdmin || (title === titleList.IPA && ipaStatus === 'OPEN')">
            <span class="btn-text">{{'COMMON.SAVE_CUSTOMER' | translate}}</span></button>
        </mat-panel-description>
      </mat-expansion-panel-header>
    </mat-expansion-panel>
    <div class="row" *ngIf="panelOpenState">
      <kt-portlet-body>
        <form class="kt-form" [formGroup]="customerLayoutForm" #ngForm="ngForm">
          <div class="kt-form__section kt-form__section--first">
            <div class="form-group row mb-0">
              <div class="col-lg-2"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.ITEM' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.LEN' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.WIDTH' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.HEIGHT' | translate}}</span></div>
              <div class="col-lg-2"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.ITEM' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.LEN' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.WIDTH' | translate}}</span></div>
              <div class="col-lg-1 text-center"><span class="lwh-title">{{'IPA.CUSTOMER_LAYOUT.HEIGHT' | translate}}</span></div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.PACK_TABLE' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packTableL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packTableL', 'maxlength') || isControlHasError('packTableL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packTableW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packTableW', 'maxlength') || isControlHasError('packTableW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packTableH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packTableH', 'maxlength') || isControlHasError('packTableH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.INBOUND' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="inboundConveyorL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.CUSTOMER_LAYOUT.FT' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('inboundConveyorL', 'maxlength') || isControlHasError('inboundConveyorL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="inboundConveyorW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('inboundConveyorW', 'maxlength') || isControlHasError('inboundConveyorW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="inboundConveyorH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('inboundConveyorH', 'maxlength') || isControlHasError('inboundConveyorH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.SHELF' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="shelfL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('shelfL', 'maxlength') || isControlHasError('shelfL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="shelfW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('shelfW', 'maxlength') || isControlHasError('shelfW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="shelfH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('shelfH', 'maxlength') || isControlHasError('shelfH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.OUTBOUND' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="outboundL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.CUSTOMER_LAYOUT.FT' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('outboundL', 'maxlength') || isControlHasError('outboundL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="outboundW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('outboundW', 'maxlength') || isControlHasError('outboundW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="outboundH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('outboundH', 'maxlength') || isControlHasError('outboundH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 required form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.SMALL_CARTON' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="smallestCartonL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('smallestCartonL', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('smallestCartonL', 'maxlength') || isControlHasError('smallestCartonL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="smallestCartonW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('smallestCartonW', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('smallestCartonW', 'maxlength') || isControlHasError('smallestCartonW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="smallestCartonH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('smallestCartonH', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('smallestCartonH', 'maxlength') || isControlHasError('smallestCartonH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.PACK_STATION' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packStationL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packStationL', 'maxlength') || isControlHasError('packStationL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packStationW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packStationW', 'maxlength') || isControlHasError('packStationW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="packStationH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('packStationH', 'maxlength') || isControlHasError('packStationH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.BOX' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="boxRackL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('boxRackL', 'maxlength') || isControlHasError('boxRackL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="boxRackW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('boxRackW', 'maxlength') || isControlHasError('boxRackW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="boxRackH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('boxRackH', 'maxlength') || isControlHasError('boxRackH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.SCALE' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleL', 'maxlength') || isControlHasError('scaleL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleW', 'maxlength') || isControlHasError('scaleW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleH', 'maxlength') || isControlHasError('scaleH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 required form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.LARGE_CARTON' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="largestCartonL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('largestCartonL', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('largestCartonL', 'maxlength') || isControlHasError('largestCartonL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="largestCartonW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('largestCartonW', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('largestCartonW', 'maxlength') || isControlHasError('largestCartonW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="largestCartonH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('largestCartonH', 'required')">
                  <p class="caption error-danger">
                    {{'AUTH.VALIDATION.REQUIRED_FIELD' | translate}}
                  </p>
                </div>
                <div *ngIf="isControlHasError('largestCartonH', 'maxlength') || isControlHasError('largestCartonH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.SCALE_TABLE' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleTableL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleTableL', 'maxlength') || isControlHasError('scaleTableL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleTableW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleTableW', 'maxlength') || isControlHasError('scaleTableW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="scaleTableH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('scaleTableH', 'maxlength') || isControlHasError('scaleTableH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.GAP_TABLE' | translate}}</label>
              <div class="col-lg-3 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="gapBetweenTable" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('gapBetweenTable', 'maxlength') || isControlHasError('gapBetweenTable', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.CART' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="cartL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('cartL', 'maxlength') || isControlHasError('cartL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="cartW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('cartW', 'maxlength') || isControlHasError('cartW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="cartH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('cartH', 'maxlength') || isControlHasError('cartH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 required form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.FLAPS' | translate}}</label>
              <mat-form-field class="col-lg-3">
                <mat-select class="form-control select-field-box" [formControl]="flaps">
                  <mat-option *ngFor="let item of flapList" [value]="item">{{item}}</mat-option>
                </mat-select>
                <mat-error *ngIf="isControlHasError('flaps', 'required')" class="ml-3">
                  <p class="caption status-danger">
                    {{'IPA.CUSTOMER_LAYOUT.VALIDATION.FLAPS' | translate}}
                  </p>
                </mat-error>
              </mat-form-field>
              <div class="col-lg-3 text-center title-content"><span class="main-title">{{'IPA.WAREHOUSE.GENERAL_QUESTION' | translate}}</span></div>
              <div class="col-lg-2 text-center"></div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.CUSTOMER_LAYOUT.LABEL.LOADING_HEIGHT' | translate}}</label>
              <div class="col-lg-3 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="materialLoadingHeight" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('materialLoadingHeight', 'maxlength') || isControlHasError('materialLoadingHeight', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-3 required form-label">{{'IPA.WAREHOUSE.LABEL.HOPPER_BOLTED' | translate}}</label>
              <div class="col-lg-2 radio-grp">
                <mat-radio-group aria-label="Select an option" class="cpa-radio" [formControl]="hoppersBolted">
                  <mat-radio-button class="cpa-radio-text" [value]="true" [checked]="hoppersBolted?.value === true">{{'COMMON.YES' | translate}}
                  </mat-radio-button>
                  <mat-radio-button class="cpa-radio-text ml-2" [value]="false" [checked]="hoppersBolted?.value === false">{{'COMMON.NO' | translate}}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.TOTE_SMALL' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteSmallL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteSmallL', 'maxlength') || isControlHasError('toteSmallL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteSmallW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteSmallW', 'maxlength') || isControlHasError('toteSmallW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteSmallH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteSmallH', 'maxlength') || isControlHasError('toteSmallH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-3 required form-label">{{'IPA.WAREHOUSE.LABEL.PACK_BOLTED' | translate}}</label>
              <div class="col-lg-2 radio-grp">
                <mat-radio-group aria-label="Select an option" class="cpa-radio" [formControl]="packTableBolted">
                  <mat-radio-button class="cpa-radio-text" [value]="true" [checked]="packTableBolted?.value === true">{{'COMMON.YES' | translate}}
                  </mat-radio-button>
                  <mat-radio-button class="cpa-radio-text ml-2" [value]="false" [checked]="packTableBolted?.value === false">{{'COMMON.NO' | translate}}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.TOTE_MED' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteMediumL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteMediumL', 'maxlength') || isControlHasError('toteMediumL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteMediumW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteMediumW', 'maxlength') || isControlHasError('toteMediumW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteMediumH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteMediumH', 'maxlength') || isControlHasError('toteMediumH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-3 required form-label">{{'IPA.WAREHOUSE.LABEL.CONVEYOR_BOLTED' | translate}}</label>
              <div class="col-lg-2 radio-grp">
                <mat-radio-group aria-label="Select an option" class="cpa-radio" [formControl]="conveyorBolted">
                  <mat-radio-button class="cpa-radio-text" [value]="true" [checked]="conveyorBolted?.value === true">{{'COMMON.YES' | translate}}
                  </mat-radio-button>
                  <mat-radio-button class="cpa-radio-text ml-2" [value]="false" [checked]="conveyorBolted?.value === false">{{'COMMON.NO' | translate}}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-2 form-label">{{'IPA.WAREHOUSE.LABEL.TOTE_LARGE' | translate}}</label>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteLargeL" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteLargeL', 'maxlength') || isControlHasError('toteLargeL', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteLargeW" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteLargeW', 'maxlength') || isControlHasError('toteLargeW', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <div class="col-lg-1 pt-4">
                <div class="input-group">
                  <input class="form-control field-box" matInput [formControl]="toteLargeH" maxlength="{{appConstants.requiredMaxLength}}" [pattern]="decimalPattern" />
                  <div class="input-group-append">
                    <span class="input-group-text">{{ 'IPA.INTEGRATION_SPECIFICATION.INCH' | translate }}</span>
                  </div>
                </div>
                <div *ngIf="isControlHasError('toteLargeH', 'maxlength') || isControlHasError('toteLargeH', 'pattern')">
                  <p class="caption error-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.INVALID_WORKSTATION' | translate}}
                  </p>
                </div>
              </div>
              <label class="col-lg-3 required form-label">{{'IPA.WAREHOUSE.LABEL.OBSTRUCTION' | translate}}</label>
              <div class="col-lg-2 radio-grp">
                <mat-radio-group aria-label="Select an option" class="cpa-radio" [formControl]="obstructions">
                  <mat-radio-button class="cpa-radio-text" [value]="true" [checked]="obstructions?.value === true">{{'COMMON.YES' | translate}}
                  </mat-radio-button>
                  <mat-radio-button class="cpa-radio-text ml-2" [value]="false" [checked]="obstructions?.value === false">{{'COMMON.NO' | translate}}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
            <div class="form-group row mb-0">
              <label class="col-lg-12 text-left required form-label">{{'IPA.WAREHOUSE.LABEL.NOTES' | translate}}</label>
              <mat-form-field class="h-auto col-sm-12 mat-form-field-fluid user-rectangle rectangle" appearance="outline">
                <textarea matInput rows="5" [formControl]="notes" maxlength="{{appConstants.descLength}}"></textarea>
                <mat-error *ngIf="isControlHasError('notes', 'required')" class="mt-5">
                  <p class="caption status-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.NOTES_REQD' | translate}}
                  </p>
                </mat-error>
                <mat-error *ngIf="isControlHasError('notes', 'maxlength')" class="ml-3">
                  <p class="caption status-danger">
                    {{'IPA.INTEGRATION_SPECIFICATION.VALIDATION.NOTES' | translate}}
                  </p>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </form>
      </kt-portlet-body>
    </div>
  </mat-accordion>
</kt-portlet>