import { Pageable } from '@shared/services/utils';

export class MaterialGroup {
  public id: number;
  public name?: string;
  public royaltyRateType?: string;
  public materialCategoryId?: number;
  public materialCategoryName?: string;
  public active?: boolean;
}

export class MaterialGroupList extends Pageable {
  content: MaterialGroup[] = [];
}

export class UnitOfMeasure {
  id: number;
  name: string;
  active: boolean;
  materialGroups: MaterialGroup[] = [];
  abbreviation: string;
  csvUnit: string;
  materialCategoryId: number;
  materialCategoryName: string;
  pricePer: string;
}

export class UnitOfMeasureFilter extends Pageable {
  content: UnitOfMeasure[] = [];
}
