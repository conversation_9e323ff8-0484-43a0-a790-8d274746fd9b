import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { <PERSON><PERSON>, AuditList, AuditFilter } from '@shared/models/audit.model';
import { PageableQuery } from '@shared/services/utils';
import { TranslatePipe } from '@ngx-translate/core';
import { AuditingService } from './auditing.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AppConstants, ENTITY, ACTION } from '@shared/constants';
import { diff } from 'json-diff';
import { FormControl } from '@angular/forms';
import moment from 'moment';

@Component({
  selector: 'app-auditing',
  templateUrl: './auditing.component.html',
  styleUrls: ['./auditing.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TranslatePipe]
})
export class AuditingComponent extends SflBaseComponent implements OnInit {
  totalItems: number;
  auditLog: Audit[] = [];
  audit: Audit = new Audit();
  pageableQuery: PageableQuery = new PageableQuery();
  viewPageableObj: PageableQuery = new PageableQuery();
  totalElements: number;
  filter: AuditFilter = new AuditFilter();
  totalPaginationObj: PageableQuery = new PageableQuery();
  activeReverse: boolean;
  showDetail: boolean[] = [];
  obj: any;
  oldData: string;
  newData: string;
  key: string;
  data = [];
  startDate = new FormControl('');
  endDate = new FormControl('');
  action = new FormControl('');
  sectionName = new FormControl('');
  modifiedBy = new FormControl('');
  sectionList = [
    {"key": ENTITY.USER, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.USER')},
    {"key": ENTITY.REGION, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.REGION')},
    {"key": ENTITY.SALES_GROUP, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.SALES_GROUP')},
    {"key": ENTITY.NATIONAL_ACCOUNT, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.NATIONAL_ACCOUNT')},
    {"key": ENTITY.CURRENCY, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.CURRENCY')},
    {"key": ENTITY.CONFIG_DATA, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.CONFIG_DATA')},
    {"key": ENTITY.QUOTE_ROUTING, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.QUOTE_ROUTING')},
    {"key": ENTITY.FRIGHT_VERBIAGE, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.FREIGHT_VERBIAGE')},
    {"key": ENTITY.INTEGRATION_CATALOG, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.INTEGRATION_CATALOG')},
    {"key": ENTITY.AP_PP_MATERIAL, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.AP_PP_MATERIAL')},
    {"key": ENTITY.INTEGRATION, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.INTEGRATION')},
    {"key": ENTITY.FP_MATERIAL, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.FP_MATERIAL')},
    {"key": ENTITY.NON_EQUIPMENT, "value": this.translatePipe.transform('AUDIT_LOGS.SECTION_LIST.NON_EQUIPMENT')},
  ];
  actionList = [
    {"key": ACTION.SAVE, "value": this.translatePipe.transform('AUDIT_LOGS.ACTION_LIST.SAVE')},
    {"key": ACTION.UPDATE, "value": this.translatePipe.transform('AUDIT_LOGS.ACTION_LIST.UPDATE')},
    {"key": ACTION.DELETE, "value": this.translatePipe.transform('AUDIT_LOGS.ACTION_LIST.DELETE')},
    {"key": ACTION.SOFTDELETE, "value": this.translatePipe.transform('AUDIT_LOGS.ACTION_LIST.SOFT_DELETE')},
    {"key": ACTION.RESTORE, "value": this.translatePipe.transform('AUDIT_LOGS.ACTION_LIST.RESTORE')},
  ];
  userId: number = +localStorage.getItem('userId');
  principalList: string[] = [];
  json = JSON;
  diff = diff;

  constructor(private readonly chRef: ChangeDetectorRef, private readonly auditService: AuditingService, private readonly translatePipe: TranslatePipe) {
    super();
  }

  ngOnInit(): void {
    this.setPageableObject(
      0
    );
    this.setViewPageableObject(
      1
    );
    this.pageableQuery.asc = AppConstants.sortByDescDirection;
    this.pageableQuery.orderBy = AppConstants.id;
    this.getAllAudits();
    this.getPrincipalList();
  }

  getAllAudits(isFilter = false) {
    this.loading$.next(true);
    this.isSubmitting = true;
    this.setFilter(isFilter);
    this.subscriptionManager.add(this.auditService.getAuditLogs(this.pageableQuery, this.filter).subscribe((audit: AuditList) => {
      this.loading$.next(false);
      this.auditLog = audit.content;
      this.auditLog.forEach((data) => {
        if (data) {
          this.obj = diff(JSON.parse(data.oldData || null), JSON.parse(data.newData || null));
          if (this.obj) {
            if (data.auditEventType.toLowerCase() === ACTION.UPDATE || data.auditEventType.toLowerCase() === ACTION.SOFTDELETE || data.auditEventType.toLowerCase() === ACTION.DELETE || data.auditEventType.toLowerCase() === ACTION.RESTORE) {
              this.data[Number(data.id)] = [];
              let i = 0;
              for (const key in this.obj) {
                this.data[Number(data.id)][i] = {key, old_data: this.obj[key]?.__old, new_data: this.obj[key]?.__new};
                i = i + 1;
              }
            } else if (data.auditEventType.toLowerCase() === ACTION.SAVE) {
              if (this.obj.__old === null && this.obj.__new !== null) {
                this.data[Number(data.id)] = [];
                let i = 0;
                for (const key in this.obj.__new) {
                  this.data[Number(data.id)][i] = {key, old_data: '-', new_data: this.obj?.__new[key]};
                  i = i + 1;
                }
              }
            }
          }
        }
      });
      this.isSubmitting = false;
      this.totalElements = audit.totalElements;
      this.showCurrentPagination(audit.pageable.pageNumber, audit.pageable.pageSize, audit.numberOfElements, audit.totalElements);
      this.viewPageableObj.size = audit.pageable.pageSize;
      this.viewPageableObj.page = audit.pageable.pageNumber + 1;
      this.chRef.detectChanges();
    }, () => {
      this.loading$.next(false);
      this.filter = new AuditFilter();
      this.chRef.detectChanges();
    }));
  }

  clickRow(i) {
    this.showDetail[i] = !this.showDetail[i];
  }

  getPrincipalList() {
    this.subscriptionManager.add(
      this.auditService.getPrincipalList().subscribe((res: string[]) => {
        this.principalList = res;
      })
    );
  }

  setFilter(isAppliedFilter = false) {
    if(isAppliedFilter) {
      this.filter.entity = this.sectionName.value ? this.sectionName.value : null;
      this.filter.startDate = this.startDate.value ? moment(this.startDate.value).format(AppConstants.dateFormat) : null;
      this.filter.endDate = this.endDate.value ? moment(this.endDate.value).format(AppConstants.dateFormat) : null;
      this.filter.eventType = this.action.value ? this.action.value : null;
      this.filter.principal = this.modifiedBy.value ? this.modifiedBy.value : null;
    }
    this.filter.userId = this.userId;
  }

  setPageableObject(
    page?: number,
    size?: number
  ) {
    this.pageableQuery.page = page;
    if (size) {
      this.pageableQuery.size = size;
    } else {
      this.pageableQuery.size = this.paginationConfig.pageSize;
    }
    this.pageableQuery.asc =  this.pageableQuery.asc;
    this.pageableQuery.orderBy =  this.pageableQuery.orderBy;
  }

  setViewPageableObject(
    page?: number,
    size?: number
  ) {
    this.viewPageableObj.page = page;
    if (size) {
      this.viewPageableObj.size = size;
    } else {
      this.viewPageableObj.size = this.paginationConfig.pageSize;
    }
    this.viewPageableObj.asc =  this.viewPageableObj.asc;
    this.viewPageableObj.orderBy =  this.viewPageableObj.orderBy;
  }

  getSorting() {
    this.setPageableObject(
      this.pageableQuery.page,
      this.pageableQuery.size
    );
    this.loading$.next(true);
    this.pageableQuery.orderBy;
    this.pageableQuery.asc = this.activeReverse;
    this.getAllAudits();
  }

  getPagination(event, isPage: boolean) {
    this.setPageableObject(
      isPage ? event - 1 : this.pageableQuery.page,
      isPage ? this.pageableQuery.size : event
    );
    this.loading$.next(true);
    this.pageableQuery.orderBy;
    this.pageableQuery.asc;
    this.getAllAudits();
  }

  showCurrentPagination(
    page?: number,
    size?: number,
    numberOfElements?: number,
    totalElements?: number
  ) {
    this.totalPaginationObj = new PageableQuery();
    this.totalPaginationObj.page = page;
    this.totalPaginationObj.size = size;
    if(numberOfElements) {
      this.totalPaginationObj.numberOfElements = numberOfElements;
    }
    if(totalElements) {
      this.totalPaginationObj.totalElements = totalElements;
    }
  }

  resetFilter() {
    this.filter = new AuditFilter();
    this.action.setValue('');
    this.startDate.setValue('');
    this.endDate.setValue('');
    this.sectionName.setValue('');
    this.modifiedBy.setValue('');
    this.getAllAudits();
  }

}
