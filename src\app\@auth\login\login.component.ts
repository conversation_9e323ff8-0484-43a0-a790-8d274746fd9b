// Angular
import {
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import {
  FormGroup,
  Validators,
  FormControl,
} from "@angular/forms";
// RxJS
import { Observable } from "rxjs/internal/Observable";
import { Subject } from "rxjs/internal/Subject";
// Translate
import { AuthNoticeService } from "@auth/auth-notice/auth-notice.service";
import { AuthService } from "@auth/auth.service";
import { SflBaseComponent } from "@shared/components/sfl-base/sfl-base.component";
import { AppConstants, APP_ROUTES, ROLE } from "@shared/constants";
import { User } from '@shared/models/user.model';
import { MessagingService } from '@shared/services/messaging.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangePasswordComponent } from '@auth/change-password/change-password.component';

@Component({
  selector: "kt-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class LoginComponent
  extends SflBaseComponent
  implements OnInit, OnDestroy {
  // Public params
  loginForm: FormGroup = this.initializeForm();
  isLoggedIn$: Observable<boolean>;
  username = this.loginForm.controls["username"];
  password = this.loginForm.controls["password"];
  redirectedTo: string;

  private readonly unsubscribe: Subject<string>;

  private returnUrl: string;

  /**
   * Component constructor
   *
   * @param router: Router
   * @param auth: AuthService
   * @param authNoticeService: AuthNoticeService
   * @param translate: TranslateService
   * @param fb: FormBuilder
   * @param cdr
   * @param route
   */
  constructor(
    private readonly router: Router,
    private readonly auth: AuthService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly messagingService: MessagingService,
    private readonly route: ActivatedRoute,
    private readonly modalService: NgbModal
  ) {
    super();
    this.unsubscribe = new Subject();
  }

  ngOnInit(): void {
    // redirect back to the returnUrl before login
    this.redirectedTo = localStorage.getItem('redirectTo');
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params.returnUrl || "/";
    });
  }

  initializeForm(): FormGroup {
    return new FormGroup({
      username: new FormControl(
        "",
        Validators.compose([
          Validators.required,
          Validators.maxLength(AppConstants.requiredMaxLength),
        ])
      ),
      password: new FormControl(
        "",
        Validators.compose([
          Validators.required
        ])
      ),
    });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this.authNoticeService.setNotice(null);
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.isSubmitting = false;
  }

  /**
   * Form Submit
   */
  submit() {
    const controls = this.loginForm.controls;
    /** check form */
    if (this.loginForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      return;
    }
    this.isSubmitting = true;
    const authData = {
      username: controls.username.value,
      password: controls.password.value,
      rememberMe: true,
    };
    this.auth
      .login(authData)
      .subscribe(() => {
        this.loading$.next(false);
        this.isSubmitting = false;
        this.subscriptionManager.add(
          this.auth.getAccount().subscribe((user: User) => {
            localStorage.setItem('roles', user.authorities.toString());
            localStorage.setItem('userId', user.id.toString());
            localStorage.setItem('name', `${user.firstName} ${user.lastName}`);
            if(user.authorities.includes(ROLE.USER) && user.titleName) {
              localStorage.setItem('titleName', user.titleName);
              localStorage.setItem('regionId', user.regionId.toString());
              localStorage.setItem('currencyId', user.currencyId.toString());
              localStorage.setItem('exchangeRate', user.currencyExchangeRate.toString());
              if(user.salesGroupId) {
                localStorage.setItem('salesGroupId', user.salesGroupId.toString());
              }
              if(user.assignedIssId) {
                localStorage.setItem('assignedIssId', user.assignedIssId.toString());
              }
              if(user.requirePasswordReset) {
                const modalRef = this.modalService.open(ChangePasswordComponent, { centered: true, backdrop: 'static', size: 'md', keyboard: false });
                modalRef.componentInstance.requirePasswordReset = user.requirePasswordReset;
              }
              this.messagingService.requestPermission();
              this.messagingService.getFirebaseToken();
              this.messagingService.receiveMessage();
            }
            if(this.redirectedTo && (this.redirectedTo.includes(APP_ROUTES.REDIRECT_CAPEX))) {
              this.router.navigateByUrl(this.redirectedTo);
            } else {
              this.router.navigateByUrl(APP_ROUTES.DASHBOARD);
            }
          })
        );
      }, () => {
        this.loading$.next(false);
        this.isSubmitting = false;
      })
  }

  /**
   * Checking control validation
   *
   * @param controlName: string => Equals to formControlName
   * @param validationType: string => Equals to valitors name
   */
  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.loginForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }
}
