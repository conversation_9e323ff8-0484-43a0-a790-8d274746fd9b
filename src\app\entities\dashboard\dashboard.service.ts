import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DashboardList } from '@shared/models/dashboard.model';
import { CapexFilter, IPAFilter, IPADashboardList, CapexList, CPAList } from '@shared/models/general-info.model';
import { PageableQuery, createRequestOption } from '@shared/services/utils';
import { ApiUrl } from '@shared/constants';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(
    private readonly http: HttpClient
  ) { }

  getDashboardList(filter: CapexFilter, pageableQuery: PageableQuery): Observable<DashboardList> {
    return this.http.post<DashboardList>(`${ApiUrl.dashboardList}${pageableQuery.orderBy}/${pageableQuery.asc}`, filter, {
      params: createRequestOption(pageableQuery)
    });
  }

  getCPADashboardList(filter: Capex<PERSON>ilter, pageableQuery: PageableQuery): Observable<CPAList> {
    return this.http.post<CPAList>(`${ApiUrl.cpaDashboardList}${pageableQuery.orderBy}/${pageableQuery.asc}`, filter, {
      params: createRequestOption(pageableQuery)
    });
  }

  getCSDashboardList(filter: CapexFilter, pageableQuery: PageableQuery): Observable<DashboardList> {
    return this.http.post<DashboardList>(`${ApiUrl.csDashboardList}${pageableQuery.orderBy}/${pageableQuery.asc}`, filter, {
      params: createRequestOption(pageableQuery)
    });
  }

  getIPAList(filter: IPAFilter, pageableQuery: PageableQuery, userId: number): Observable<IPADashboardList> {
    return this.http.post<IPADashboardList>(`${ApiUrl.ipaList}${userId}/${pageableQuery.orderBy}/${pageableQuery.asc}`, filter, {
      params: createRequestOption(pageableQuery)
    });
  }

  shareCapex(id: number, sharedWith: number) {
    return this.http.get(`${ApiUrl.shareCapex}${id}/${sharedWith}`);
  }

  shareFPCapex(id: number, sharedWith: number) {
    return this.http.get(`${ApiUrl.fpShareCapex}${id}/${sharedWith}`);
  }
}
