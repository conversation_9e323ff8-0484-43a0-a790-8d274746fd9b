import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { ProductInfo, MachineInfo, Smartline } from '@shared/models/ipa.model';
import { AppConstants, TITLE } from '@shared/constants';
import { ApPpIpaService } from '../ap-pp-ipa.service';
import { TranslatePipe } from '@ngx-translate/core';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { saveAs } from 'file-saver';
import { IntegrationPicture } from '@shared/models/cpa.model';
import { MatDialog } from '@angular/material/dialog';
import { MatSidenav } from '@angular/material/sidenav';
import { ShowImageComponent } from '../show-image/show-image.component';

@Component({
  selector: 'app-smartline',
  templateUrl: './smartline.component.html',
  styleUrls: ['./smartline.component.scss']
})
export class SmartlineComponent extends SflBaseComponent implements OnInit {
  smartlineState: boolean;
  panelOpenState: boolean[] = [];
  @Input() capexId: number;
  @Input() currencyId: number;
  @Output() openSidebar = new EventEmitter<SidebarParams<Smartline>>();
  @Input() versionID: number;
  @Input() ipaStatus: string;
  @Input() projectNumber: string;
  @Input() designEngineer: string;
  @Input() isAdmin: boolean;
  picSidebarParams: SidebarParams<IntegrationPicture>;
  isPicVisible: boolean;
  integrationPicture: IntegrationPicture[][] = [];
  ipaMaterial: ProductInfo = new ProductInfo();
  ipaMaterialList: ProductInfo[] = [];
  ipaMaterialId: number;
  versionId: number;
  annualRevenue: number;
  ipaEquipment: MachineInfo = new MachineInfo();
  ipaMachineList: MachineInfo[] = [];
  ipaEquipmentId: number;
  usedWithSystem = new Array();
  equipmentUsedWithSystem = new Array();
  totalMachineQuantity: number;
  addButton = AppConstants.addImage;
  id: number;
  userId = +localStorage.getItem('userId');
  smartlineList: Smartline[] = [];
  smartlineData: Smartline = new Smartline();
  disableAdd: boolean;
  title = localStorage.getItem('titleName');
  titleName = TITLE
  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly ipaService: ApPpIpaService,
    private readonly translatePipe: TranslatePipe,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly matDialog: MatDialog
  ) { 
    super();
  }

  ngOnInit(): void {
    this.subscriptionManager.add(
      this.ipaService.getIPASubject().subscribe(res => {
        if (res) {
          this.versionId = res.ipaVersionId;
          this.getSmartlineList();
          if(res.fst) {
            this.disableAdd = false;
          }
        }
      })
    );
    this.subscriptionManager.add(
      this.ipaService.getDataSubject().subscribe(res => {
        if (res) {
          this.getSmartlineList();
          this.cdf.detectChanges();
        }
      })
    );
    this.getSubject();
  }

  getSubject() {
    this.subscriptionManager.add(
      this.ipaService.getValidFormSubject().subscribe(res => {
        if(res) {
          this.disableAdd = res;
        }
      })
    );
  }

  openPicSidebar(sidebarParams, template: MatSidenav, id: number): void {
    this.id = id;
    if (this.integrationPicture[id].length >= 2) {
      this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CPA.ADD_PIC.MAX_LIMIT'), AlertType.Error);
    } else {
      this.isPicVisible = true;
      this.picSidebarParams = sidebarParams;
      template.toggle();
    }
  }

  sidebarPicClosed(isClosed: boolean, template: MatSidenav) {
    if (isClosed) {
      this.isPicVisible = false;
      template.toggle();
    }
  }

  getIntegrationPicture(id) {
    this.subscriptionManager.add(
      this.ipaService.getIntegrationPicture(id).subscribe((res: IntegrationPicture[]) => {
        this.integrationPicture[id] = res;
        this.cdf.detectChanges();
      }, () => {
        this.cdf.detectChanges();
      })
    );
  }

  getSmartlineList() {
    this.subscriptionManager.add(
      this.ipaService.getSmartline(this.versionId).subscribe((res: Smartline[]) => {
        this.smartlineList = res;
        res.forEach((data) => {
          this.smartlineData = data;
          this.ipaMachineList = data.ipaEquipments;
          this.ipaMaterialList = data.ipaMaterials;
          this.totalMachineQuantity = data.totalMachineQuantity;
          this.annualRevenue = data.totalMaterialAnnualRevenue;
          this.ipaService.setDeletedItem(data.itemDeleted);
          this.id = data.id;
          data?.ipaMaterials.forEach((obj, index) => {
            this.usedWithSystem[index] = obj?.usedWithSystem;
          });
          data?.ipaEquipments.forEach((obj, index) => {
            this.equipmentUsedWithSystem[index] = obj?.usedWithSystem;
          });
          this.getIntegrationPicture(data.id);
        });
        this.cdf.detectChanges();
      }, () => { 
      })
    );
  }

  openSmartlineSidebar(data) {
    const params = {data: data, isEdit: data ? true : false};
    this.openSidebar.emit(params);
  }

  handleSystem(event, item, i) {
    this.ipaMaterial.usedWithSystem = this.usedWithSystem[i];
    this.ipaMaterial.airPlusPaperPlusMaterialId = item.airPlusPaperPlusMaterialId ;
    this.ipaMaterial.annualRevenue = item.annualRevenue;
    this.ipaMaterial.description = item.description;
    this.ipaMaterial.materialGroupId = item.materialGroupId;
    this.ipaMaterial.number = item.number;
    this.ipaMaterial.id = item.id;
    this.ipaMaterial.ipaIntegrationId = this.id;
    this.subscriptionManager.add(
      this.ipaService.updateIpaMaterial(this.ipaMaterial, this.ipaMaterial.id, event.value).subscribe((res) => {
        this.getUpdatedList(res);
      }, () => {
        this.isSubmitting = false;
        this.cdf.detectChanges();
      })
    );
  }

  handleEquipmentSystem(event, item, i) {
    this.ipaEquipment.equipmentId = item.equipmentId ;
    this.ipaEquipment.quantity = item.quantity;
    this.ipaEquipment.description = item.description;
    this.ipaEquipment.number = item.number;
    this.ipaEquipment.id = item.id;
    this.ipaEquipment.ipaIntegrationId = this.id;
    this.ipaEquipment.usedWithSystem = this.equipmentUsedWithSystem[i];
    this.subscriptionManager.add(
      this.ipaService.updateIpaEquipment(this.ipaEquipment, this.ipaEquipment.id, event.value).subscribe((res) => {
        this.getUpdatedList(res);
      }, () => {
        this.isSubmitting = false;
        this.cdf.detectChanges();
      })
    );
  }

  getUpdatedList(res) {
    if(res) {
      this.isSubmitting = false;
      this.getSmartlineList();
      this.cdf.detectChanges();
    }
  }

  deletePicture(id: number, integrationId: number) {
    const _title = this.translatePipe.transform('NON_EQUIPMENT.DELETE_DIALOG.TITLE');
    const _description = this.translatePipe.transform('AP_PP_CPA.DEL_PIC.BODY');
    const _deleteMessage = this.translatePipe.transform('AP_PP_CPA.DEL_PIC.SUCCESS');
    const dialogRef = this.layoutUtilsService.deleteElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(this.ipaService.deleteIPAPicture(id).subscribe(() => {
        this.layoutUtilsService.showActionNotification(_deleteMessage, AlertType.Success);
        this.getIntegrationPicture(integrationId);
      }, (err) => this.onError(err)))
    });
  }

  showImage(data) {
    this.matDialog.open(ShowImageComponent, { data: data, disableClose: true, maxHeight: '100vh' });
  }

  getFile(url: string, filename: string) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'arraybuffer';
    xhr.onload = function () {
      if (this.status === 200) {
        saveAs(new File([this.response], filename));
      }
    };
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
    xhr.send();
  }

  download(url) {
    this.getFile(url, url.split('/')[7]);
    this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.IPA_DOWNLOAD'));
  }

  deleteQuestion() {
    const _title = this.translatePipe.transform('NON_EQUIPMENT.DELETE_DIALOG.TITLE');
    const _description = this.translatePipe.transform('IPA.CUSTOM_INTEGRATION.DELETE.BODY');
    const _deleteMessage = this.translatePipe.transform('IPA.CUSTOM_INTEGRATION.DELETE.SUCCESS');

    const dialogRef = this.layoutUtilsService.deleteElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(this.ipaService.deleteSmartline(this.id, this.userId).subscribe(() => {
        this.layoutUtilsService.showActionNotification(_deleteMessage, AlertType.Success);
        this.getSmartlineList();
      }, (err) => this.onError(err)))
    });
  }

  onError(err) {
    this.isSubmitting = false;
    this.layoutUtilsService.showActionNotification(this.translatePipe.transform(err.error.message), AlertType.Error);
    this.cdf.detectChanges();
  }

}
