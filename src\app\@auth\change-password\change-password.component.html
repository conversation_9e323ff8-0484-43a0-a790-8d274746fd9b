<div class="card">
  <div class="modal-header">
    <h5 class="modal-title profile-name">
      {{ 'COMMON.CHANGE_PASSWORD_BTN' | translate }}
    </h5>
  </div>
  <form class="form-horizontal" #userProfile="ngForm" ngbAutofocus [formGroup]="changePasswordForm" (ngSubmit)="changePassword()">
    <div class="modal-body">
      <div class="form-group" *ngIf="!showCurrentPassword">
        <mat-form-field class="rectangle password-field form-field col-12 pl-0 pr-0" appearance="outline">
          <input type="password" matInput placeholder="{{ 'AUTH.RESET_PASSWORD.LABEL.CURRENT_PASSWORD' | translate }}" (input)="handlePassword()" [formControl]="currentPassword" autocomplete="off" sflPasswordEye />
          <mat-error *ngIf="isControlHasError('currentPassword', 'required')" class="mt-1">
            <strong>{{ 'AUTH.VALIDATION.INVALID' | translate: { name: 'AUTH.RESET_PASSWORD.LABEL.CURRENT_PASSWORD' | translate | lowercase } }}</strong>
          </mat-error>
          <mat-error *ngIf="changePasswordForm.get('currentPassword').errors && changePasswordForm.get('currentPassword').errors.MatchPassword" class="mt-1">
            <strong>{{ 'AUTH.RESET_PASSWORD.VALIDATION.NO_PASSWORD_MATCH' | translate }}</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group pt-6 mt-5">
        <mat-form-field floatLabel="always" class="rectangle password-field form-field col-12 pl-0 pr-0" appearance="outline">
          <input matInput type="password" placeholder="{{ 'AUTH.RESET_PASSWORD.LABEL.NEW_PASSWORD' | translate }}" (input)="handlePassword()" [formControl]="newPassword" autocomplete="off" sflPasswordEye [pattern]="passwordPattern" />
          <mat-error *ngIf="isControlHasError('newPassword', 'required') || isControlHasError('newPassword', 'pattern')" class="mt-1">
            <strong>{{ 'AUTH.VALIDATION.INVALID' | translate: { name: 'AUTH.RESET_PASSWORD.LABEL.NEW_PASSWORD' | translate | lowercase } }} {{'AUTH.RESET_PASSWORD.VALIDATION.PASSWORD_PATTERN' | translate}}</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group pt-6 mt-5">
        <mat-form-field floatLabel="always" class="rectangle password-field form-field col-12 pl-0 pr-0" appearance="outline">
          <input matInput type="password" (input)="handlePassword()" placeholder="{{ 'AUTH.RESET_PASSWORD.LABEL.CONFIRM_PASSWORD' | translate }}" [formControl]="confirmPassword" autocomplete="off" sflPasswordEye [pattern]="passwordPattern" />
          <mat-error *ngIf="isControlHasError('confirmPassword', 'required') || isControlHasError('confirmPassword', 'pattern')" class="mt-1">
            <strong>{{ 'AUTH.VALIDATION.INVALID' | translate: { name: 'AUTH.RESET_PASSWORD.LABEL.CONFIRM_PASSWORD' | translate | lowercase } }}</strong>
          </mat-error>
          <mat-error *ngIf="changePasswordForm.get('confirmPassword').errors && changePasswordForm.get('confirmPassword').errors.MatchPassword" class="mt-1">
            <strong>{{ 'AUTH.RESET_PASSWORD.VALIDATION.NO_MATCH' | translate: { name: 'AUTH.INPUT.PASSWORD' | translate | lowercase } }}</strong>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary cancel-btn" (click)="closeProfileModal()" *ngIf="!requirePasswordReset">
        {{ 'COMMON.CANCEL' | translate }}
      </button>
      <button type="submit" class="btn btn-primary logout-btn" [disabled]="isSubmitting" [isSubmitting]="isSubmitting">
        {{ 'COMMON.CHANGE_PASSWORD_BTN' | translate }}
      </button>
    </div>
  </form>
</div>
