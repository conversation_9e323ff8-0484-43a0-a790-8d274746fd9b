import { Pageable } from '@shared/services/utils';
import { EquipmentCapex, FPEquipmentCapex } from './equipment.model';
import { MaterialInfoCapex, FPMaterialInfoCapex } from './material.model';
import { IntegrationSystemCapex } from './integration-system.model';
import { NonEquipmentCapex } from './non-equipment.model';
import { Summary, FreightInfo } from './summary.model';

export class GeneralInfo {
  capexDate: Date;
  capexLastModifiedDate: Date;
  creationInfo: CreationInfo = new CreationInfo();
  approvalInfo: GeneralApprovalInfo = new GeneralApprovalInfo();
  lastModifiedDate: Date;
  priceGuaranteeTerm: number;
  priceGuaranteeTermApplied: boolean;
  id: number;
  issId: number;
  fastLane: boolean;
  itemDeleted: boolean;
  shipToName: string;
  shipToNumber: string;
  shipToTypeEnum: string;
  soldToName: string;
  soldToNumber: string;
  totalAmount: number;
  currencyId: number;
  currency: string;
  exchangeRate: number;
}

export class CreationInfo {
  approvalStatus: string;
  approverId: number;
  approverName: string;
  clonedFromId: number;
  createdById: number;
  createdByName: string;
  globalAccount: boolean;
  nationalAccount: boolean;
  nationalAccountManagerId: number;
  nationalAccountManagerName: string;
  nationalAccountProgramId: number;
  nationalAccountProgramName: string;
  nationalAccountProgramNumber: string;
  nationalAcocunt: boolean;
  number: string;
  previousVersionId: number;
  salesGroupId: number;
  salesGroupName: string;
  salesPersonId: number;
  salesPersonName: string;
  version: number;
  capexType: string;
  startDate: string;
}

export class CapexDTO {
  approvalStatus: string;
  approvalRequired: boolean;
  approvedOrRejectedOn: string;
  archived: boolean;
  approver: string;
  approverId: number;
  approverTitle: string;
  capexDate: string;
  capexLastModifiedDate: string;
  capexType: string;
  currency: string;
  color: string;
  creator: string;
  creatorId: number;
  creatorTitle: string;
  customer: string;
  distributor: string;
  endDate: string;
  id: number;
  lastModifiedDate: string;
  nationalAccountManager: string;
  nationalAccountManagerId: number;
  number: string;
  reasonApprovalRequired: string;
  salesGroupId: number;
  salesGroupNumber: number;
  salesPerson: string;
  salesPersonId: number;
  salesPersonManagerId: number;
  sentForApprovalOn: string;
  startDate: string;
  shipToNumber: string;
  soldToNumber: string;
  totalAmount: number;
  totalMarginPercent: number;
  selected?: boolean;
}

export class CPAData {
  approvalStatus: string;
  capexArchived: boolean;
  capexContactName: string;
  capexDate: string;
  capexDeleted: boolean;
  capexId: number;
  capexNumber: string;
  capexType: string;
  color: string;
  cpaType: string;
  createdBy: string;
  createdById: number;
  globalAccount: boolean;
  id: number;
  installationDate: string;
  lastModifiedBy: string;
  lastModifiedOn: string;
  nationalAccount: boolean;
  nationalAccountManager: string;
  nationalAccountManagerId: number;
  priceGuaranteeTerm: number;
  priceGuaranteeTermApplied: boolean;
  regionalSalesManager: string;
  regionalSalesManagerId: number;
  shipToContactName: string;
  shipToName: string;
  shipToNumber: string;
  soldToContactName: string;
  soldToName: string;
  soldToNumber: string;
  territoryManager: string;
  territoryManagerId: number;
  totalMarginAmount: number;
  totalMarginPercent: number;
  version: number;
  currencyId: number;
  creatorTitle: string;
}

export class CPAList extends Pageable {
  content: CPAData[] = [];
}

export class CapexList extends Pageable {
  content: CapexDTO[] = [];
}

export class CapexFilter {
  approvalStatus: string;
  currency: string;
  customer: string;
  distributor: string;
  dashboard?: boolean;
  endDate: Date;
  startDate: Date;
  userId: number;
  capexType: string;
  archived: boolean;
  deleted: boolean;
  salesPersonId: number;
  color: string;
  shipToNumber: string;
  soldToNumber: string;
  nationalAccountProgramId?: number;
}

export class CSFilter {
  capexIds: number[] = [];
  capexType: string;
  userId: number;
}

export class IPAFilter {
  createdBy: string;
  createdOn: string;
  dashboard: boolean;
  ipaStatus: string;
  lastModifiedBy: string;
  lastModifiedOn: string;
  nationalAccountManager: string;
  regionalSalesManager: string;
  requestDate: Date;
  territoryManager: string;
  userId: number;
  shipToName: string;
  projectNumber: string;
}

export class IPADashboard { 
  capexDate: string;
  capexId: number;
  capexNumber: string;
  createdBy: string;
  createdById: number;
  createdOn: string;
  id: number;
  ipaId: number;
  ipaStatus: string;
  lastModifiedOn: string;
  nationalAccountManager: string;
  nationalAccountManagerId: number;
  number: string;
  projectNumber: string;
  regionalSalesManager: string;
  regionalSalesManagerId: number;
  requestDate: string;
  shipToName: string;
  shipToNumber: string;
  soldToName: string;
  soldToNumber: string;
  territoryManager: string;
  territoryManagerId: number;
  version: number;
}

export class IPADashboardList extends Pageable {
  content: IPADashboard[] = [];
}

export class ApprovalInfo {
  approvalRequired: boolean;
  approvalStatus: string;
  approved: boolean;
  approvedOrRejectedOn: string;
  approverId: number;
  approverName: string;
  approverTitle: string;
  color: string;
  reasonApprovalRequired: string;
  quoteRoutingRuleFreightColor: string;
  quoteRoutingRuleFreightId: number;
  quoteRoutingRuleMarginColor: string;
  quoteRoutingRuleMarginId: number;
}

export class Capex {
  airPlusPaperPlusEquipmentInfoSection: EquipmentCapex = new EquipmentCapex();
  airPlusPaperPlusMaterialInfoSection: MaterialInfoCapex = new MaterialInfoCapex();
  generalInfoSection: GeneralInfo = new GeneralInfo();
  integrationSystemInfoSection: IntegrationSystemCapex = new IntegrationSystemCapex();
  nonEquipmentMaterialInfoSection: NonEquipmentCapex = new NonEquipmentCapex();
  summaryInfoSection: Summary = new Summary();
  freightInfo: FreightInfo = new FreightInfo();
  creationInfo: CreationInfo = new CreationInfo();
  approvalInfo: ApprovalInfo = new ApprovalInfo();
  id: number;
  approvalStatus: string;
  archived: boolean;
  deleted: boolean;
  totalAmount: number;
  totalMarginAmount: number;
  totalMarginPercent: number;
  totalRevenue: number;
}

export class FPCapex {
  foamPlusMaterialInfoSection: FPMaterialInfoCapex = new FPMaterialInfoCapex();
  foamPlusEquipmentInfoSection: FPEquipmentCapex = new FPEquipmentCapex();
  generalInfoSection: GeneralInfo = new GeneralInfo();
  summaryInfoSection: Summary = new Summary();
  freightInfo: FreightInfo = new FreightInfo();
  creationInfo: CreationInfo = new CreationInfo();
  approvalInfo: ApprovalInfo = new ApprovalInfo();
  id: number;
  approvalStatus: string;
  archived: boolean;
  deleted: boolean;
  totalAmount: number;
  totalMarginAmount: number;
  totalMarginPercent: number;
  totalRevenue: number;
}

export class Currency {
  abbreviation: string;
  exchangeRate: number;
  id: number;
  name: string;
  symbol: string
}

export class GeneralApprovalInfo {
  approvalRequired: boolean;
  approvalStatus: string;
  approved: boolean;
  approvedOrRejectedOn: string;
  approverId: number;
  approverName: string;
  approverTitle: string;
  color: string;
  reasonApprovalRequired: string;
  sentForApprovalOn: string;
  reasonForRejection: string;
  csReasonForRejection: string;
}

export class ISSRegion {
  id: number;
  name: string;
  active: boolean;
}
