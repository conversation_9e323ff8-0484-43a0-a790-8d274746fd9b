.form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  margin-top: 20px;
  text-align: right;
}

.invalid {
  color: red;
}

.text-form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
}

::ng-deep.mat-form-field-appearance-legacy .mat-form-field-wrapper {
  padding-bottom: 0.85em !important;
}

.input-group-text {
  height: 40px !important;
}

.main-title {
  height: 20px;
  color: #1a1a1a;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
  line-height: 73px;
}

.field-box {
  height: 40px !important;
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
  padding: 0 0.25em 0 0.75em !important;
}

.select-field-box {
  height: 40px !important;
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
}

::ng-deep.mat-input-element {
  caret-color: #d0d7e1 !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  background-color: #f3f6f9 !important;
  color: #1a1a1acf !important;
}

.radio-grp {
  padding-top: 2rem;
}

.tab-title {
  box-sizing: border-box;
  height: 30px;
  border: 1px solid #D0D7E1;
  background-color: #F3F5F8;
  text-align: center;
  line-height: 25px;
}

.type-box {
  box-sizing: border-box;
  height: 200px;
  border: 1px solid #D0D7E1;
  border-radius: 2px;
  background-color: #F3F5F8;
}

.field-size {
  max-width: 8.2% !important;
}

.btn-height {
  height: 40px;
}
