import { Injectable } from '@angular/core';
import { SflSortConfig } from '../constants/sort.constants';

@Injectable({
  providedIn: 'root'
})
export class SflSortConfigService {
  CONFIG_OPTIONS: SflSortConfig;

  constructor(moduleConfig?: SflSortConfig) {
    this.CONFIG_OPTIONS = {
      ...new SflSortConfig(),
      ...moduleConfig
    };
  }

  getConfig(): SflSortConfig {
    return this.CONFIG_OPTIONS;
  }
}
