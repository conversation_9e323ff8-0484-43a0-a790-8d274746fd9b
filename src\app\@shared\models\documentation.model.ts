export class Documentation {
  capexId: number;
  capexNumber: string;
  createdById: number;
  createdByName: string;
  createdOn: string;
  documents: DocumentDTO[] = [];
  id: number;
  lastModifiedById: number;
  lastModifiedByName: string;
  lastModifiedOn: string;
  name: string;
}

export class DocumentDTO {
  capexId: number;
  capexNumber: string;
  createdById: number;
  createdByName: string;
  createdOn: string;
  docPath: string;
  documentGroupId: number;
  documentGroupName: string;
  documentType: string;
  fileName: string;
  id: number;
  lastModifiedById: number;
  lastModifiedByName: string;
  lastModifiedOn: string;
  mimeType: string;
  notes: string;
  url: string;
  version: number;
}

export class DocumentList {
  docs: DocumentDTO[] = [];
}

export class AddUpdateDocumentsResponse {
  id: number;
  name: string;
  createdOn: string;
  lastModifiedOn: string;
  capexId: number;
  capexNumber: string;
  createdById: number;
  createdByName: string;
  lastModifiedById: number;
  lastModifiedByName: string;
  documents: DocumentDTO[];
}
