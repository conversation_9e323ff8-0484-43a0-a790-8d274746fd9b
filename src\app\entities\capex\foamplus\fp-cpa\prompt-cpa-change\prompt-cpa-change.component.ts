import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-prompt-cpa-change',
  templateUrl: './prompt-cpa-change.component.html',
  styleUrls: ['./prompt-cpa-change.component.scss']
})
export class PromptCpaChangeComponent implements OnInit {

  constructor(public readonly dialogRef: MatDialogRef<PromptCpaChangeComponent>) { }

  ngOnInit(): void {
  }

  close(proceed: boolean) {
    this.dialogRef.close(proceed);
  }

}
