.mat-expansion-panel {
  border: 1px solid #e6ebef !important;
}

.mat-expansion-panel:not([class*="mat-elevation-z"]) {
  box-shadow: none !important;
}

::ng-deep.mat-expansion-panel-body {
  padding: 0 !important;
}

.mat-expansion-panel-header.mat-expanded {
  height: 48px !important;
}

.mat-expansion-panel-header-description {
  flex-grow: 0 !important;
}

.top-class {
  margin-top: 1.2rem;
}

.table-responsive {
  padding: 25px;
}

thead.tableHeader {
  background-color: #ffffff !important;
}

.table-border {
  border: 1px solid #ebedf3;
}

::ng-deep.card-label {
  color: #073968 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.mat-card.scrollable-content {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mat-card.scrollable-content mat-card-content {
  overflow-y: auto;
}
.mat-card.scrollable-content mat-card-title {
  display: block;
}

.form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
  margin-top: 15px;
}

.invalid {
  color: red;
}

.text-form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
}

::ng-deep.mat-form-field-appearance-legacy .mat-form-field-wrapper {
  padding-bottom: 0.85em !important;
}

.input-group-text {
  height: 40px !important;
}

.field-box {
  height: 40px !important;
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
  padding: 0 0.25em 0 0.75em !important;
}

.select-field-box {
  height: 40px !important;
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
}

::ng-deep.mat-input-element {
  caret-color: #d0d7e1 !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  background-color: #f3f6f9 !important;
  color: #1a1a1acf !important;
}

.radio-grp {
  padding-top: 1rem;
}

.tab-title {
  box-sizing: border-box;
  height: 30px;
  border: 1px solid #D0D7E1;
  background-color: #F3F5F8;
  text-align: center;
  line-height: 25px;
}

.type-box {
  box-sizing: border-box;
  height: 200px;
  border: 1px solid #D0D7E1;
  border-radius: 2px;
  background-color: #F3F5F8;
}

.hopper-label {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.hopper-text {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  text-align: center;
}

.main-title {
  height: 20px;
  color: #1a1a1a;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
}

.deleted-color {
  color: #e40c0c;
}

.mat-drawer-content {
  overflow-x: hidden !important;
}

.overhead-title {
  line-height: 35px;
}

.add-btn {
  height: 35px;
}

.open-table {
  padding-left: 40px;
  padding-right: 40px;
}

.no-pic {
  color: #949494;
  font-size: 16px;
  font-weight: 500;
}

.img-size {
  width: 250px;
  height: 210px;
}

.caption-class {
  margin-top: 1rem;
  font-size: 14px;
  font-weight: 500;
  margin-left: 1rem;
  word-break: break-word;
  width: 250px;
}

.img-wrap {
  position: relative;
}

.img-wrap .close {
  position: absolute;
  right: 10px;
  z-index: 100;
  top: 7px;
  background: #ffffff;
  border-radius: 50%;
  padding: 3px;
  cursor: pointer;
  padding-left: 6px;
  padding-right: 6px;
  opacity: 1;
}

.close {
  color: #1a1a1a !important;
  float: right;
  font-size: 15px;
  font-weight: bold;
}