import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { AppConstants } from '@shared/constants';
import { ShipToLocation } from '@shared/models/cpa.model';
import { ApPpCpaService } from '../ap-pp-cpa.service';
import { TranslatePipe } from '@ngx-translate/core';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';

@Component({
  selector: 'app-add-ship-to',
  templateUrl: './add-ship-to.component.html',
  styleUrls: ['./add-ship-to.component.scss']
})
export class AddShipToComponent extends SflBaseComponent implements OnInit, OnChanges {
  @Output() closeSidebarEvent: EventEmitter<boolean> = new EventEmitter();
  @Input() sidebarParams: SidebarParams<ShipToLocation>;
  @Output() getShipToLocation = new EventEmitter();
  addShipForm: FormGroup = this.initializeForm();
  shipNo = this.addShipForm.controls['shipNo'];
  address = this.addShipForm.controls['address'];
  city = this.addShipForm.controls['city'];
  state = this.addShipForm.controls['state'];
  zipCode = this.addShipForm.controls['zipCode'];
  commonPattern = AppConstants.commonPattern;
  numberPattern = AppConstants.onlyNumberPattern;
  shipToLocation: ShipToLocation = new ShipToLocation();
  id: number;
  @Input() cpaId: number;
  locationTitle: number;

  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly cpaService: ApPpCpaService,
    private readonly translatePipe: TranslatePipe,
    private readonly layoutUtilsService: LayoutUtilsService
  ) { 
    super();
  }

  ngOnInit(): void {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
    this.setComponentTitle();
    if (simpleChanges.sidebarParams && simpleChanges.sidebarParams.currentValue) {
      if (this.sidebarParams.isEdit) {
        this.id = this.sidebarParams.data.id;
      }
      this.getShipLocationDetails();
    }
  }

  getShipLocationDetails() {
    if(this.id) {
      this.loading$.next(true);
      this.subscriptionManager.add(
        this.cpaService.getShipToLocationById(this.id).subscribe((res: ShipToLocation) => {
          this.shipToLocation = res;
          this.setForm(res);
          this.loading$.next(false);
          this.cdf.detectChanges();
        }, () => {
          this.loading$.next(false);
          this.cdf.detectChanges();
        })
      );
    }
  }

  setComponentTitle() {
    this.locationTitle = this.sidebarParams.isEdit ? this.translatePipe.transform('AP_PP_CPA.UPDATE_SHIP_LOC') : this.translatePipe.transform('AP_PP_CPA.ADD_SHIP_LOC');
  }

  initializeForm() {
    return new FormGroup({
      shipNo: new FormControl('', [Validators.required]),
      address: new FormControl('', [Validators.required]),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipCode: new FormControl('', [Validators.required])
    });
  }

  closeSidebar() {
    this.closeSidebarEvent.next(true);
  }

  setForm(shipLocation: ShipToLocation) {
    this.addShipForm.setValue({
      city: shipLocation.city,
      shipNo: shipLocation.shipToNumber,
      state: shipLocation.state,
      address: shipLocation.street,
      zipCode: shipLocation.zipCode
    });
  }

  addData(): ShipToLocation {
    const shipLocation = new ShipToLocation();
    shipLocation.id = this.id ? this.id : null;
    shipLocation.cpaId = this.cpaId;
    shipLocation.city = this.city.value;
    shipLocation.shipToNumber = this.shipNo.value;
    shipLocation.state = this.state.value;
    shipLocation.street = this.address.value;
    shipLocation.zipCode = this.zipCode.value;
    return shipLocation;
  }

  submit() {
    const controls = this.addShipForm.controls;
    if (this.addShipForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      return;
    }
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.cpaService.addShipToLocation(this.addData()).subscribe((res: ShipToLocation) => {
        this.isSubmitting = false;
        this.shipToLocation = res;
        if(this.id) {
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CPA.UPDATE'));
        } else {
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CPA.SUCCESS'));
        }
        this.getShipToLocation.emit();
        this.closeSidebar();
        this.cdf.detectChanges();
      }, () => {
        this.isSubmitting = false;
        this.cdf.detectChanges();
      })
    );
  }

  onError() {
    this.isSubmitting = false;
    this.cdf.detectChanges();
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.addShipForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }

}
