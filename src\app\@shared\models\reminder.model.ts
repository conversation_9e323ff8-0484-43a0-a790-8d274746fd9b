import { Pageable } from '@shared/services/utils';

export class Reminder {
  amount: number;
  capexDate: string;
  capexNumber: string;
  capexId: number;
  capexType: string;
  customer: string;
  daysRemaining: number;
  distributor: string;
  expiryDate: string;
  id: number;
  issId: number;
  issName: string;
  managerId: number;
  managerName: string;
  nationalAccountManagerId: number;
  nationalAccountManagerName: string;
  readByISS: boolean;
  readByManager: boolean;
  readByNationalAccountManager: boolean;
  readBySalesPerson: boolean;
  readByVicePresident: boolean;
  salesPersonId: number;
  salesPersonName: string;
  vicePresidentId: number;
  vicePresidentName: string;
}

export class ReminderList extends Pageable {
  content: Reminder[] = [];
}
