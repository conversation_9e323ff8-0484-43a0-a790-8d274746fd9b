export class Summary {
  freightInfo: FreightInfo = new FreightInfo();
  id: number;
  notes: string;
  totalMonthlyMarginPercent?: number;
  totalMonthlyMarginValue?: number;
  totalMonthlySalesMaterial?: number;
  averageLeaseToOwnMonthsAllocated?: number;
  marginOverviewMaterialAndFilmGTM?: number;
  marginOverviewMaterialAndFilmGTMPercent?: number;
  marginOverviewSystemLeaseServiceGTM?: number;
  marginOverviewSystemLeaseServiceGTMPercent?: number;
  perSystemPerMonthMargin: number;
  perSystemPerMonthRevenue: number;
}

export class FreightInfo {
  freightInstructions: string;
  localFreightPolicy: boolean;
}

export class FreightVerbiage {
  date: string;
  fileName: string;
  id: number;
  pdfUrl: string;
}
