<mat-sidenav-container>
  <mat-sidenav [ngClass]="isPicVisible ? 'position-fixed' : ''" class="detail-sidebar" #sidebarInsertPic mode="over" position="end" disableClose>
    <app-insert-picture (getIntegrationPicture)="getSAMList()" (closeSidebarEvent)="sidebarPicClosed($event, sidebarInsertPic)" [cpaId]="id" *ngIf="isPicVisible"
      [sidebarParams]="picSidebarParams" [isIPA]="true"></app-insert-picture>
  </mat-sidenav>
  <mat-sidenav-content class="detail-sidebar-content">
    <kt-portlet>
      <mat-accordion>
        <mat-expansion-panel (opened)="panelOpenState = true" [expanded]="conceptSAMList?.length ? true : false" (closed)="panelOpenState = false">
          <mat-expansion-panel-header>
            <mat-panel-title class="header-title">
              {{'IPA.CONCEPT_SAM.TITLE' | translate}} {{conceptSAMList?.length ? '- ' + conceptSAMList?.length : ''}}
            </mat-panel-title>
            <mat-panel-description *ngIf="panelOpenState" (click)="$event.stopPropagation();" class="ml-4">
              <button mat-raised-button *ngIf="title !== titleList.VIEWER_C1" color="primary" [disabled]="ipaStatus !== ipaConst.OPEN || disableAdd || isAdmin" class="btn btn-primary add-btn" (click)="openSAMSidebar(null)">
                <span><img [src]="addButton" alt="add-new" /></span><span class="btn-text ml-2">{{'EQUIPMENT.ADD_BUTTON' | translate}}</span></button>
            </mat-panel-description>
          </mat-expansion-panel-header>
        </mat-expansion-panel>
        <div class="row" *ngIf="panelOpenState">
          <div class="col-md-12" *ngIf="!conceptSAMList?.length">
            <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
              {{ 'IPA.PRODUCT_INFO.NO_DATA' | translate }}
            </h6>
          </div>
          <div class="col-md-12 pb-5 open-table" *ngFor="let item of conceptSAMList; let i=index">
            <mat-accordion>
              <mat-expansion-panel (opened)="samState[i] = true" (closed)="samState[i] = false">
                <mat-expansion-panel-header class="overhead-title">
                  <mat-panel-title>
                    {{item?.number | replaceSymbol}}
                  </mat-panel-title>
                  <mat-panel-description *ngIf="samState[i]" (click)="$event.stopPropagation();" class="ml-4">
                    <button mat-raised-button color="primary" [disabled]="ipaStatus !== ipaConst.OPEN || isAdmin" class="btn btn-primary add-btn mr-4" (click)="openSAMSidebar(item)">
                      <span class="btn-text">{{'COMMON.UPDATE' | translate}}</span></button>
                    <button mat-raised-button color="primary" [disabled]="ipaStatus !== ipaConst.OPEN || isAdmin" class="btn btn-primary add-btn" (click)="deleteQuestion()">
                      <span class="btn-text">{{'COMMON.DELETE' | translate}}</span></button>
                    <button type="button" mat-raised-button color="primary" class="btn btn-primary add-btn ml-3" [disabled]="ipaStatus === 'CLOSED' || isAdmin"
                      (click)="openPicSidebar($event, sidebarInsertPic, item?.id)">
                      <span><em class="fas fa-upload"></em></span><span class="btn-text">{{'AP_PP_CPA.CPA_PICTURES.INSERT_BTN' | translate}}</span></button>
                  </mat-panel-description>
                </mat-expansion-panel-header>
              </mat-expansion-panel>
              <div class="card-body p-5" *ngIf="samState[i]">
                <div class="row">
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.NO_CUST_CARTON' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.numberOfCustomerCartonSizes}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.MIN_CARTON_WEIGHT' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.minimumCartonWeight}} lbs</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.MAX_CARTON_WEIGHT' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.maximumCartonWeight}} lbs</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.AVG_CARTON' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.averageCartonsPerMinute}}</span>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.EXISTING_ZAP' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.existingZpaConveyor === true ? 'Yes' : 'No'}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.NO_ZONES' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.numberOfConveyorZones}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.DS_MOUNT' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.mountToConveyorType ? (item?.mountToConveyorType | replaceSymbol | titlecase) : 'NONE'}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.LENGTH_CONVEYOR' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.lengthOfConveyorSectionToReplace}} ft.</span>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.INSIDE_CONVEYOR' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.insideConveyorWidth}} in.</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.ROLLER_SPACING_CENTER' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.rollerSpacingCenterToCenter}} in.</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.OBSTRUCTION' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.obstructionsUnderConveyor === true ? 'Yes' : 'No'}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.CONVEYOR_ROLLER' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.conveyorHeightToTopOfRoller}} in.</span>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.FPM' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.conveyorSpeedFPM}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.CARTON_JUSTIFIED' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.cartonJustified === true ? 'Yes' : 'No'}}</span>
                  </div>
                  <div class="col-md-3">
                    <label class="col-md-12 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.HOW' | translate}}:</label>
                    <span class="col-md-12 hopper-text">{{item?.cartonJustification}}</span>
                  </div>
                </div>
                <div class="mt-4">
                  <div class="col-md-12">
                    <div class="row">
                      <div class="col-md-3"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.ITEM' | translate}}(inches)</span></div>
                      <div class="col-md-1 text-center"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.LEN' | translate}}</span></div>
                      <div class="col-md-1 text-center"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.WIDTH' | translate}}</span></div>
                      <div class="col-md-1 text-center"><span class="main-title">{{'IPA.CUSTOMER_LAYOUT.HEIGHT' | translate}}</span></div>
                    </div>
                    <div class="row mt-2">
                      <label class="col-md-3 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.MIN_CARTON_SIZE' | translate}}:</label>
                      <span class="col-md-1 hopper-text">{{item?.minimumCartonSize?.length}}</span>
                      <span class="col-md-1 hopper-text">{{item?.minimumCartonSize?.width}}</span>
                      <span class="col-md-1 hopper-text">{{item?.minimumCartonSize?.height}}</span>
                    </div>
                    <div class="row mt-2">
                      <label class="col-md-3 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.MAX_CARTON_SIZE' | translate}}:</label>
                      <span class="col-md-1 hopper-text">{{item?.maximumCartonSize?.length}}</span>
                      <span class="col-md-1 hopper-text">{{item?.maximumCartonSize?.width}}</span>
                      <span class="col-md-1 hopper-text">{{item?.maximumCartonSize?.height}}</span>
                    </div>
                    <div class="row mt-2">
                      <label class="col-md-3 hopper-label">{{'IPA.CONCEPT_SAM.LABEL.TOTAL_CONVEYOR_WIDTH' | translate}}:</label>
                      <span class="col-md-1 hopper-text"></span>
                      <span class="col-md-1 hopper-text">{{item?.totalConveyorWidth}}</span>
                      <span class="col-md-1 hopper-text">{{item?.totalConveyorHeight}}</span>
                    </div>
                  </div>
                </div>
                <div class="row pt-4">
                  <div class="col-md-6">
                    <div class="tab-title col-md-12">{{'IPA.PRODUCT_INFO.MATERIAL_TITLE' | translate}}</div>
                    <table class="table table-border user-table dt-responsive nowrap" aria-describedby="product-open-table">
                      <thead class="tableHeader">
                        <tr>
                          <th class="text-center open-header-text" id="materialNo">{{'AP_PP_CPA.PRICE_END_USER.LABEL.MATERIAL' | translate}}
                          </th>
                          <th class="text-center open-header-text" id="desc">{{'AP_PP_CPA.PRICE_END_USER.LABEL.DESC' | translate}}
                          </th>
                          <th class="text-center open-header-text" id="yearlyRevenue">{{'IPA.PRODUCT_INFO.LABEL.ROLLS' | translate}}
                          </th>
                          <th class="text-center open-header-text system-text" id="system">{{'IPA.PRODUCT_INFO.LABEL.USED_SYSTEM' | translate}}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let item of ipaMaterialList; let j=index">
                          <td class="text-center open-body-text" [ngClass]="item?.itemDeleted ? 'deleted-color' : ''">{{item?.number}}</td>
                          <td class="text-center open-body-text">{{item?.description}}</td>
                          <td class="text-center open-body-text">{{item?.annualRevenue | currency: currencyId === 1 ? appConstants.currency : appConstants.canadaCurrency}}</td>
                          <td class="text-center open-body-text">
                            <div>
                              <mat-radio-group #usedWithSystem1="ngModel" aria-label="Select an option" class="cpa-radio" id="usedWithSystem{{j}}" name="usedWithSystem{{j}}"
                                [(ngModel)]="usedWithSystem[j]" (change)="handleSystem($event, item, j)">
                                <mat-radio-button [value]="true" class="cpa-radio-text" [checked]="item?.usedWithSystem === true">{{'COMMON.YES' | translate}}
                                </mat-radio-button>
                                <mat-radio-button [value]="false" class="cpa-radio-text ml-2" [checked]="item?.usedWithSystem === false">{{'COMMON.NO' | translate}}
                                </mat-radio-button>
                              </mat-radio-group>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                      <tfoot *ngIf="ipaMaterialList?.length">
                        <tr>
                          <td class="text-center footer-text">{{'COMMON.TOTALS' | translate}}</td>
                          <td colspan="1"></td>
                          <td class="text-center footer-text">{{annualRevenue | currency: currencyId === 1 ? appConstants.currency : appConstants.canadaCurrency}}</td>
                          <td colspan="1"></td>
                        </tr>
                      </tfoot>
                    </table>
                    <div class="col-12" *ngIf="!ipaMaterialList?.length">
                      <div class="row">
                        <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
                          {{ 'IPA.PRODUCT_INFO.NO_DATA' | translate }}
                        </h6>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="tab-title col-md-12">{{'IPA.PRODUCT_INFO.MACHINE_TITLE' | translate}}</div>
                    <table class="table table-border user-table dt-responsive nowrap" aria-describedby="product-open-table">
                      <thead class="tableHeader">
                        <tr>
                          <th class="text-center open-header-text" id="materialNo">{{'IPA.PRODUCT_INFO.LABEL.MACHINE' | translate}}
                          </th>
                          <th class="text-center open-header-text" id="desc">{{'AP_PP_CPA.PRICE_END_USER.LABEL.DESC' | translate}}
                          </th>
                          <th class="text-center open-header-text" id="yearlyRevenue">{{'IPA.PRODUCT_INFO.LABEL.QTY' | translate}}
                          </th>
                          <th class="text-center open-header-text system-text" id="system">{{'IPA.PRODUCT_INFO.LABEL.USED_SYSTEM' | translate}}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let item of ipaMachineList; let z=index">
                          <td class="text-center open-body-text" [ngClass]="item?.itemDeleted ? 'deleted-color' : ''">{{item?.equipmentIndexedNumber}}</td>
                          <td class="text-center open-body-text">{{item?.description}}</td>
                          <td class="text-center open-body-text">{{item?.quantity}}</td>
                          <td class="text-center open-body-text">
                            <div>
                              <mat-radio-group #equipmentUsedWithSystem1="ngModel" aria-label="Select an option" class="cpa-radio" id="equipmentUsedWithSystem{{z}}"
                                name="equipmentUsedWithSystem{{z}}" [(ngModel)]="equipmentUsedWithSystem[z]" (change)="handleEquipmentSystem($event, item, z)">
                                <mat-radio-button [value]="true" class="cpa-radio-text" [checked]="item?.usedWithSystem === true">{{'COMMON.YES' | translate}}
                                </mat-radio-button>
                                <mat-radio-button [value]="false" class="cpa-radio-text ml-2" [checked]="item?.usedWithSystem === false">{{'COMMON.NO' | translate}}
                                </mat-radio-button>
                              </mat-radio-group>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                      <tfoot *ngIf="ipaMachineList?.length">
                        <tr>
                          <td class="text-center footer-text">{{'COMMON.TOTALS' | translate}}</td>
                          <td colspan="1"></td>
                          <td class="text-center footer-text">{{totalMachineQuantity}}</td>
                          <td colspan="1"></td>
                        </tr>
                      </tfoot>
                    </table>
                    <div class="col-12" *ngIf="!ipaMachineList?.length">
                      <div class="row">
                        <h6 class="col-12 d-flex justify-content-center align-items-center loader-height card-subtitle sub-title-font-style">
                          {{ 'IPA.PRODUCT_INFO.NO_DATA' | translate }}
                        </h6>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row pt-4">
                  <label class="col-md-12 hopper-label">{{'IPA.UNDERTABLE_CART.LABEL.NOTES' | translate}}:</label>
                  <span class="col-md-12 mt-2">{{item?.additionalNotes ? item?.additionalNotes : 'No notes found'}}</span>
                </div>
                <div class="row pt-4 mb-4" *ngIf="integrationPicture[item?.id]?.length">
                  <div class="tab-title text-left col-md-12">{{'AP_PP_CPA.CPA_PICTURES.IPA_TITLE' | translate}}</div>
                  <div class="card-body">
                    <div class="row">
                      <div *ngFor="let data of integrationPicture[item?.id]">
                        <div class="img-wrap ml-5">
                          <span class="close" (click)="deletePicture(data?.id, data?.ipaIntegrationId)">&times;</span>
                          <img [src]="data?.url" alt="no-img" class="img-size" title="Click to preview" (click)="showImage(data)" />
                        </div>
                        <div class="caption-class">
                          {{data?.caption}}
                        </div>
                        <div><a class="unique-link ml-4" (click)="download(data?.url)">{{'COMMON.DOWNLOAD' | translate}}</a></div>
                      </div>
                    </div>
                    <h6 class="col-12 d-flex justify-content-center no-pic align-items-center loader-height card-subtitle sub-title-font-style" *ngIf="!integrationPicture?.length">
                      {{'AP_PP_CPA.CPA_PICTURES.NO_PIC_FOUND' | translate}}
                    </h6>
                  </div>
                </div>
                <div class="col-md-12 pt-4" *ngIf="ipaStatus !== ipaConst.OPEN">
                  <app-comment-quotes [currencyId]="currencyId" [versionId]="versionId" [projectNumber]="projectNumber" [designEngineer]="designEngineer" [integrationId]="item?.id" [userId]="userId"></app-comment-quotes>
                </div>
              </div>
            </mat-accordion>
          </div>
        </div>
      </mat-accordion>
    </kt-portlet>
  </mat-sidenav-content>
</mat-sidenav-container>