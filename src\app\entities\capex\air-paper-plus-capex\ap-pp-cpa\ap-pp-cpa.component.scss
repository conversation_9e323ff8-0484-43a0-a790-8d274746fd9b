$color-custom-primary: #f3f5f8;
$color-custom-container: #f7f8fa;

.mat-expansion-panel {
  border: 1px solid #e6ebef !important;
}

.copy-size {
  font-size: 12px !important;
}

.mat-expansion-panel:not([class*="mat-elevation-z"]) {
  box-shadow: none !important;
}

::ng-deep.mat-expansion-panel-body {
  padding: 0 !important;
}

.mat-expansion-panel-header.mat-expanded {
  height: 48px !important;
}

.open-table {
  padding-left: 25.5px;
  padding-right: 25.5px;
}

.table-responsive {
  padding: 25px;
}

thead.tableHeader {
  background-color: #ffffff !important;
}

.btn-section {
  margin-top: -1.4rem !important;
  width: 18% !important;
  margin-left: 1rem;
}

.cpa-label {
  color: #1a1a1a;
  font-size: 15px;
  font-weight: 500;
  margin-top: 1rem;
}

.cpa-radio {
  line-height: 40px;
}

.cpa-radio-text {
  color: #1a1a1a;
  font-size: 13px;
  font-weight: 500;
}

.ship-class {
  color: #1a1a1a;
  font-size: 13px;
  font-weight: 500;
  line-height: 21px;
  margin-left: 1.5rem;
}

.save-btn {
  border-radius: 2px !important;
  height: 34px;
  float: right;
}

.user-table {
  border: 1px solid #ebedf3;
}

.field-box {
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
  padding: 0 0.25em 0 0.75em !important;
}

.form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
  margin-top: 2rem;
}

@media (min-width: 900px) {
  .cpa-label, .form-label {
    text-align: right;
  }
}

@media (max-width: 1000px) {
  .cpa-label, .form-label {
    text-align: left;
    margin-top: 1rem !important;
  }
}

.cpa-machine {
  color: #484848;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}

.no-pic {
  color: #949494;
  font-size: 16px;
  font-weight: 500;
}

::ng-deep.mat-checkbox-label {
  color: #1a1a1a !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.header-table {
  padding-left: 36.5px;
  padding-right: 36.5px;
}

.flag-class {
  height: 20px;
  width: 30px;
  margin-right: 1rem;
}

.position-fixed {
  position: fixed !important;
}

mat-sidenav.detail-sidebar {
  width: 450px;
}

mat-sidenav-container {
  height: auto !important;
}

.mat-drawer-content {
  overflow-x: hidden !important;
}

.deleted-color {
  color: #e40c0c;
}

.error-danger {
  font-size: 87% !important;
  color: #d71010 !important;
  margin-top: 0.5rem;
}

.img-size {
  width: 250px;
  height: 210px;
}

.caption-class {
  margin-top: 1rem;
  font-size: 14px;
  font-weight: 500;
  margin-left: 1rem;
  word-break: break-word;
  width: 250px;
}

.img-wrap {
  position: relative;
}

.img-wrap .close {
  position: absolute;
  right: 10px;
  z-index: 100;
  top: 7px;
  background: #ffffff;
  border-radius: 50%;
  padding: 3px;
  cursor: pointer;
  padding-left: 6px;
  padding-right: 6px;
  opacity: 1;
}

.close {
  color: #1a1a1a !important;
  float: right;
  font-size: 15px;
  font-weight: bold;
}
