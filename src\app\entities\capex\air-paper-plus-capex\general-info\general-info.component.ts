import { Component, OnInit, ChangeDetectorRef, Input, Output, EventEmitter, AfterViewInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { TranslatePipe } from '@ngx-translate/core';
import { GeneralInfo, Currency, ISSRegion, Capex, FPCapex } from '@shared/models/general-info.model';
import { AirPaperPlusCapexService } from '../air-paper-plus-capex.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { NationalAccount, User } from '@shared/models/user.model';
import { FILTER_OPERATOR, ENTITY, STATUS, AppConstants, TITLE, CURRENCY, APP_ROUTES, ROLE } from '@shared/constants';
import { FilterObject, TitleId, Filter, RegionId } from '@shared/models/filter.model';
import { UserService } from '@entities/user-management/user.service';
import { PageableQuery } from '@shared/services/utils';
import { Summary } from '@shared/models/summary.model';
import { Router } from '@angular/router';
import { FoamplusService } from '@entities/capex/foamplus/foamplus.service';
import { NonEquipmentInfo } from '@shared/models/non-equipment.model';
import { ExistingIPA, ApproveOrRejectDTO } from '@shared/models/dashboard.model';
import { ApPpIpaService } from '../ap-pp-ipa/ap-pp-ipa.service';
import { LayoutService } from '@entities/partials/layout.service';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { DefaultCPA } from '@shared/models/cpa.model';
import { ApPpCpaService } from '../ap-pp-cpa/ap-pp-cpa.service';
import { MasterDataService } from '@entities/master-data-management/master-data.service';
import { map, pairwise, startWith } from 'rxjs/operators';

@Component({
  selector: 'sfl-general-info',
  templateUrl: './general-info.component.html',
  styleUrls: ['./general-info.component.scss'],
  providers: [TranslatePipe]
})
export class GeneralInfoComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  shipType = [{ value: this.translatePipe.transform('AP_PP_CAPEX.SHIP_TYPE.NEW') }, { value: this.translatePipe.transform('AP_PP_CAPEX.SHIP_TYPE.CURRENT') }];
  generalInfoForm: FormGroup = this.initializeForm();
  deviationDate = this.generalInfoForm.controls['deviationDate'];
  endNumber = this.generalInfoForm.controls['endNumber'];
  endName = this.generalInfoForm.controls['endName'];
  distributorNumber = this.generalInfoForm.controls['distributorNumber'];
  distributorName = this.generalInfoForm.controls['distributorName'];
  currentShipType = this.generalInfoForm.controls['currentShipType'];
  nationalAccountId = this.generalInfoForm.controls['nationalAccountId'];
  nationalAccountName = this.generalInfoForm.controls['nationalAccountName'];
  naManager = this.generalInfoForm.controls['naManager'];
  isNA = this.generalInfoForm.controls['isNA'];
  isGA = this.generalInfoForm.controls['isGA'];
  storoPackTM = this.generalInfoForm.controls['storoPackTM'];
  currency = this.generalInfoForm.controls['currency'];
  generalInfo: GeneralInfo = new GeneralInfo();
  id: number;
  createdId: number;
  name: string;
  holdPeriodPattern = AppConstants.holdPeriodPattern;
  capexId: number;
  @Input() capexAPId: number;
  @Output() openSidebar = new EventEmitter<SidebarParams<ApproveOrRejectDTO>>();
  filter: FilterObject = new FilterObject();
  nationalAccountList: NationalAccount[] = [];
  namList: NationalAccount[] = [];
  pageableQuery: PageableQuery = new PageableQuery();
  naId: number;
  namId: number;
  naName: string;
  salesPersonByTitle: User[] = [];
  tmList: User[] = [];
  titleName: string;
  isTM: boolean;
  numberPattern = AppConstants.numberPattern;
  generalInfoTitle: string;
  regionId: number;
  currencyList: Currency[] = [];
  currencyId: number;
  @Input() summaryInfo: Summary;
  @Input() summaryForm: FormGroup;
  capexColor: string;
  approvalStatus: string;
  issRegion: ISSRegion = new ISSRegion();
  issRegionId: number;
  route: string;
  nonEquipmentData: NonEquipmentInfo[] = [];
  materialData = [];
  equipmentData = [];
  archived: boolean;
  existingIPA: ExistingIPA = new ExistingIPA();
  btnDisabled: boolean;
  sendToCS: boolean;
  disableCSBtn: boolean;
  disableApproveBtn: boolean;
  disableCAPEX: boolean;
  isArchived: boolean;
  disableArchiveBtn: boolean;
  isArchiveSubmit: boolean;
  title: string = localStorage.getItem('titleName');
  assignedIssId: string = localStorage.getItem('assignedIssId');
  isSubmitApprovalBtn: boolean;
  isApproveOrReject = false;
  userId = +localStorage.getItem('userId');
  approverId: number;
  titleConst = TITLE;
  sendApprovalMessage = this.translatePipe.transform('DASHBOARD.SEND_APPROVAL');
  enableApprovalBtn: boolean;
  isNANumber: boolean;
  tmSearchField = new FormControl('');
  namSearchField = new FormControl('');
  revertBtn: boolean;
  role = localStorage.getItem('roles');
  isFastLane: boolean = JSON.parse(localStorage.getItem('isFastlane'));
  isDeletedItem: boolean;
  salesGroupId: number = +localStorage.getItem('salesGroupId');
  showConversion: boolean;
  exchangeRate: number;
  currencyExchangeRate = +localStorage.getItem('exchangeRate');
  today: Date;
  applyPGT = new FormControl(false);
  cpaList: DefaultCPA[] = [];
  previousSelectedCurrency = '';
  isTMlistShown: boolean;

  public isValueEdit: boolean;
  isDisableCAPEXField = false;
  constructor(private readonly translatePipe: TranslatePipe, private readonly cdf: ChangeDetectorRef, private readonly capexService: AirPaperPlusCapexService, private readonly layoutUtilsService: LayoutUtilsService,
    private readonly userService: UserService, private readonly router: Router, private readonly fpService: FoamplusService, private readonly masterDataService: MasterDataService,
    private readonly ipaService: ApPpIpaService, private readonly messagingService: LayoutService, private readonly cpaService: ApPpCpaService) {
    super();
    this.isValueEdit = false;
  }
  ngAfterViewInit(): void {
    if (this.titleName === this.titleConst.VIEWER_C1 && !this.isFastLane) {
      this.isDisableCAPEXField = true;
      this.currency.disable();
      this.isNA.disable();
      this.isGA.disable();
      this.applyPGT.disable();
      this.deviationDate.disable();
      this.distributorNumber.disable();
      this.endNumber.disable();
      this.storoPackTM.disable();
      this.distributorName.disable();
      this.endName.disable();
      this.currentShipType.disable();
    } else {
      this.isDisableCAPEXField = false
    }
  }

  async ngOnInit(): Promise<void> {
    this.createdId = +localStorage.getItem('userId');
    this.name = localStorage.getItem('name');
    this.today = this.currentDateUTC();
    this.titleName = localStorage.getItem('titleName');
    this.route = this.router.url;
    if (this.router.url === APP_ROUTES.AP_PP_CAPEX || this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX)) {
      this.subscriptionManager.add(
        this.capexService.getCapexIdSubject().subscribe(async (res) => {
          this.capexId = res;
          this.id = res;
          this.existIPA();
          await this.getGeneralInfo(this.capexId);
          // this.isValueEdit = false;
        })
      );
      if (this.capexAPId) {
        this.existIPA();
        await this.getGeneralInfo(this.capexAPId);
        // this.isValueEdit = false;
      }
      this.subscriptionManager.add(
        this.capexService.getCapexSubject().subscribe((res: Capex) => {
          if (res.id) {
            this.nonEquipmentData = res.nonEquipmentMaterialInfoSection.pagedData.content;
            this.materialData = res.airPlusPaperPlusMaterialInfoSection.pagedData.content;
            this.equipmentData = res.airPlusPaperPlusEquipmentInfoSection.pagedData.content;
            this.isDeletedItem = res.generalInfoSection.itemDeleted;
            this.archived = res.archived;
            this.approverId = res.approvalInfo.approverId;
            if (res.archived) {
              this.capexService.setDisableCapexSubject(true);
              this.disableCAPEX = true;
              this.disableArchiveBtn = true;
              this.isSubmitApprovalBtn = false;
            }
            this.cdf.detectChanges();
          }
        })
      );
      this.getCAPEX();
    } else {
      this.getFPData();
      this.getFPCAPEX();
    }
    if(!this.storoPackTM.value) {
      this.regionId = +localStorage.getItem('regionId');
    }
    this.getDataByTitle();
    this.getCurrency();
    this.getSalesPerson();
    this.nationalAccountId.disable();
    this.naManager.disable();
    this.getAllNationalAccount();
    if ((this.isNA.value && !this.isFastLane) || this.generalInfo.creationInfo.nationalAccount) {
      this.nationalAccountName.setValidators(Validators.required);
    } else {
      this.nationalAccountName.clearValidators();
    }
    this.nationalAccountName.updateValueAndValidity();
    this.generalInfoTitle = this.translatePipe.transform('AP_PP_CAPEX.GENERAL_INFO');
    this.handleOnRegionBasis();
    this.subscriptionManager.add(
      this.tmSearchField.valueChanges.subscribe(() => {
        this.filterTM();
      })
    );
    this.subscriptionManager.add(
      this.namSearchField.valueChanges.subscribe(() => {
        this.filterNAM();
      })
    );
    if(this.isFastLane) {
      this.isNA.disable();
      this.endNumber.disable();
      this.endName.disable();
      this.distributorNumber.disable();
      this.distributorName.disable();
      this.storoPackTM.disable();
      this.currentShipType.disable();
    }
    if(this.isFastLane && (!this.capexId && !this.capexAPId)) {
      this.submit();
    }
    if(this.currencyId === CURRENCY.CAD) {
      this.showConversion = true;
      this.exchangeRate = this.currencyExchangeRate;
    } else {
      this.showConversion = false;
    }
    if(!this.capexId || !this.capexAPId) {
      this.applyPGT.disable();
    }
// Check that any form is update  -
    this.subscriptionManager.add(
      this.capexService.getEditFormStatusSubject().subscribe((res) => {
        this.isValueEdit = res;
      })
    );
  }

  handlePGT(event) {
    this.setvalueEdit();
    if(event) {
      this.subscriptionManager.add(
        this.capexService.applyPGT(this.capexId ?? this.capexAPId, event.checked).subscribe(() => {
          this.capexService.setCapexId(this.capexId ? this.capexId : this.capexAPId);
          if(this.approvalStatus === STATUS.APPROVED) {
            this.sendToCS = true;
            this.disableCSBtn = false;
          }
          this.cdf.detectChanges();
        })
      );
    }
  }

  getCPAList() {
    this.subscriptionManager.add(
      this.cpaService.getCPAVersionList(this.capexId || this.capexAPId).subscribe((res: DefaultCPA[]) => {
        this.cpaList = res;
        if(([TITLE.RSM, TITLE.ISS, TITLE.ADMIN, TITLE.SUPPORT].includes(this.title)) && res?.length) {
          this.applyPGT.enable();
        } else {
          this.applyPGT.disable();
        }
        this.applyPGT.updateValueAndValidity();
        this.cdf.detectChanges();
      }, () => {
        this.cdf.detectChanges();
      })
    );
  }

  handleFormChange(event) {
    if(event.type === AppConstants.change) {
      this.ipaService.setSubmitStatusSubject(false);
      this.capexService.setFormSubject(this.generalInfoForm);
      this.cdf.detectChanges();
    }
  }

  getSubmitAccess() {
    this.subscriptionManager.add(
      this.capexService.checkSubmit(this.capexId ?? this.capexAPId).subscribe((res: boolean) => {
        this.enableApprovalBtn = res;
        this.cdf.detectChanges();
      })
    );
  }

  filterTM() {
    this.salesPersonByTitle = [];
    this.salesPersonByTitle = this.tmList.filter(data => {
      if (this.tmSearchField.value) {
        if(data.name.toLowerCase().includes(this.tmSearchField.value) || data.name.includes(this.tmSearchField.value)) {
          this.salesPersonByTitle.push(data);
          return this.salesPersonByTitle;
        }
      } else {
        return this.tmList;
      }
    });
  }

  filterNAM() {
    this.nationalAccountList = [];
    this.nationalAccountList = this.namList.filter(data => {
      if (this.namSearchField.value) {
        if(data.name.toLowerCase().includes(this.namSearchField.value) || data.name.includes(this.namSearchField.value)) {
          this.nationalAccountList.push(data);
          return this.nationalAccountList;
        }
      } else {
        return this.namList;
      }
    });
  }

  handleSalesPerson(event) {
    this.setvalueEdit();
    this.storoPackTM.setValue(event.value.name);
    this.generalInfo.creationInfo.salesPersonId = event.value.id;
    this.regionId = event.value.regionId;
    if(!this.isFastLane) {
      if(event.value.regionId === RegionId.CANADA_ID) {
        this.currency.enable();
        this.currency.setValue(CURRENCY.CAD);
        this.currencyId = CURRENCY.CAD;
      } else {
        this.currency.setValue(1);
        this.currencyId = CURRENCY.USD;
        this.currency.disable();
      }
      this.getRegionId(event.value.regionId);
    }
    this.capexService.setRegionIdSubject(event.value.regionId);
    this.currency.updateValueAndValidity();
    if (this.generalInfoForm.valid) {
      this.handleGeneralInfo();
    }
  }

  getRegionId(regionId) {
    if (regionId === AppConstants.canadaRegionID) {
      this.endNumber.setValidators([Validators.min(AppConstants.minShipToNumberCanada), Validators.max(AppConstants.maxShipToNumberCanada)]);
      this.distributorNumber.setValidators([Validators.min(AppConstants.minSoldToNumberCanada), Validators.max(AppConstants.maxSoldToNumberCanada)]);
    } else {
      this.endNumber.setValidators([Validators.min(AppConstants.minShipToNumberUS), Validators.max(AppConstants.maxShipToNumberUS)]);
      this.distributorNumber.setValidators([Validators.min(AppConstants.minSoldToNumberUS), Validators.max(AppConstants.maxSoldToNumberUS)]);
    }
    this.endNumber.updateValueAndValidity();
    this.distributorNumber.updateValueAndValidity();
    this.cdf.detectChanges();
  }

  handleOnRegionBasis() {
    if(!this.isFastLane) {
      if(this.regionId === RegionId.CANADA_ID) {
        this.currency.enable();
      } else {
        this.currency.setValue(1);
        this.currency.disable();
      }
    }
    this.capexService.setRegionIdSubject(this.regionId);
    this.currency.updateValueAndValidity();
  }

  getDataByTitle() {
    if (!this.capexAPId && !this.capexId) {
      this.currencyId = +localStorage.getItem('currencyId');
    }
    if (this.titleName === TITLE.TM || this.titleName === TITLE.FPS) {
      this.isTM = true;
    } else {
      this.isTM = false;
      this.storoPackTM.setValidators(Validators.required);
    }
    if (this.titleName === TITLE.NAM || this.titleName === TITLE.ISS_NATIONAL) {
      this.isNA.setValue(true);
      this.isNA.disable();
      this.naManager.setValue(this.name);
      this.nationalAccountName.setValidators([Validators.required]);
    }
    if(this.titleName === TITLE.ISS_GLOBAL) {
      this.isGA.disable();
      this.isGA.setValue(true);
      this.isNA.disable();
      this.nationalAccountName.clearValidators();
    }
    this.isNA.updateValueAndValidity();
    this.isGA.updateValueAndValidity();
    this.getRegionId(this.regionId);
    if (this.currencyId === CURRENCY.USD) {
      this.currency.setValue(CURRENCY.USD);
    } else {
      this.currency.setValue(CURRENCY.CAD);
    }
  }

  getFPData() {
    this.subscriptionManager.add(
      this.capexService.getFPCapexIdSubject().subscribe((res) => {
        this.capexId = res;
        this.id = res;
        this.getFPGeneralInfo(this.capexId);
      })
    );
    if (this.capexAPId) {
      this.getFPGeneralInfo(this.capexAPId);
    }
    this.subscriptionManager.add(
      this.capexService.getFPCapexSubject().subscribe((res: FPCapex) => {
        if (res.id) {
          this.materialData = res.foamPlusMaterialInfoSection.pagedData.content;
          this.equipmentData = res.foamPlusEquipmentInfoSection.pagedData.content;
          this.isDeletedItem = res.generalInfoSection.itemDeleted;
          this.archived = res.archived;
          this.approverId = res.approvalInfo.approverId;
          if (res.archived) {
            this.capexService.setDisableCapexSubject(true);
            this.disableCAPEX = true;
            this.disableArchiveBtn = true;
            this.isSubmitApprovalBtn = false;
          }
          this.cdf.detectChanges();
        }
      })
    );
  }

  getCAPEX() {
    this.subscriptionManager.add(
      this.capexService.getCapexSubject().subscribe((res: Capex) => {
        this.getData(res);
      })
    );
  }

  getFPCAPEX() {
    this.subscriptionManager.add(
      this.capexService.getFPCapexSubject().subscribe((res: FPCapex) => {
        this.getData(res);
      })
    );
  }

  getData(res) {
    if (res.id) {
      this.getRegionId(this.regionId);
      this.archived = res.archived;
      this.approverId = res.approvalInfo.approverId;
      if (res.approvalInfo.approvalStatus !== STATUS.OPEN && res.approvalInfo.approvalStatus !== STATUS.REJECTED && res.approvalInfo.approvalStatus !== STATUS.REJECTED_CS) {
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
      } else {
        this.capexService.setDisableCapexSubject(false);
        this.disableCAPEX = false;
      }
    }
    if (res.archived) {
      this.capexService.setDisableCapexSubject(true);
      this.disableCAPEX = true;
      this.disableArchiveBtn = true;
      this.isSubmitApprovalBtn = false;
    }
    if (this.title !== TITLE.VP) {
      this.isSubmitApprovalBtn = true;
      this.isApproveOrReject = false;
      this.btnDisabled = true;
    }
    this.isDeletedItem = res.generalInfoSection.itemDeleted;
    this.cdf.detectChanges();
  }

  existIPA() {
    this.subscriptionManager.add(
      this.ipaService.existIPA(this.capexId ?? this.capexAPId).subscribe((res: ExistingIPA) => {
        this.existingIPA = res;
        this.cdf.detectChanges();
      })
    );
  }

  sendForApproval() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.SUBMIT_APPROVAL.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.SUBMIT_APPROVAL.BODY');
    if(this.isDeletedItem) {
      this.layoutUtilsService.showActionNotification(this.translatePipe.transform('COMMON.DELETED_ITEM.RESTRICT_APPROVAL'), AlertType.Error);
      return;
    }
    if(this.summaryForm?.invalid) {
      this.layoutUtilsService.showActionNotification(this.translatePipe.transform('COMMON.MISSING_ITEM'), AlertType.Error);
      return;
    }
    if (this.existingIPA.exists === true && (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) && this.isValueEdit) {
      this.ipaExists();
    } else {
      const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
      dialogRef.afterClosed().subscribe(res => {
        if (!res) {
          return;
        }
        if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
          this.subscriptionManager.add(this.capexService.saveGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
            this.approve(res);
          }, () => this.onError()));
        } else {
          this.subscriptionManager.add(this.capexService.saveFPGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
            this.approve(res);
          }, () => this.onError()));
        }
      })
    }
  }

  approve(res) {
    this.getGeneralInfoData(res);
    if(res) {
      this.submitForApproval();
    }
  }

  ipaExists() {
    const ipaTitle = this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA_MODAL.TITLE');
    const ipaDesc = this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA_MODAL.BODY');
    const dialog = this.layoutUtilsService.submitElement(ipaTitle, ipaDesc);
    dialog.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
        this.subscriptionManager.add(this.capexService.saveGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
          this.approveIfIPA(res);
        }, () => this.onError()));
      } else {
        this.subscriptionManager.add(this.capexService.saveFPGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
          this.approveIfIPA(res);
        }, () => this.onError()));
      }
    });
  }

  approveIfIPA(res) {
    this.getGeneralInfoData(res);
    if(res) {
      this.dataIfIPAExist();
    }
  }

  getGeneralInfoData(res) {
    this.isSubmitting = false;
    this.generalInfo = res;
    this.cdf.detectChanges();
  }

  dataNotIPA() {
    this.messagingService.setUnreadCountSubject(0);
    if (this.title !== TITLE.VP) {
      if (this.approvalStatus === STATUS.APPROVED && this.approverId === this.userId) {
        this.sendToCS = true;
      } else {
        this.isSubmitApprovalBtn = true;
        this.btnDisabled = true;
        this.isApproveOrReject = false;
        this.sendToCS = false;
        this.disableArchiveBtn = true;
      }
      this.capexService.setDisableCapexSubject(true);
      this.disableCAPEX = true;
    }
  }

  submitForApproval() {
    this.subscriptionManager.add(
      this.capexService.sendForApproval(this.capexId ?? this.capexAPId, this.userId).subscribe(() => {
      this.isSubmitting = false;
      if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
        if (this.summaryInfo) {
          this.capexService.saveSummaryInfo(this.summaryInfo).subscribe((res) => {
            if(res) {
              this.capexService.setCapexId(this.generalInfo.id);
            }
          });
        } else {
          this.capexService.setCapexId(this.generalInfo.id);
        }
      } else {
        if (this.summaryInfo) {
          this.capexService.saveFPSummaryInfo(this.summaryInfo).subscribe((res) => {
            if(res) {
              this.capexService.setFPCapexId(this.generalInfo.id);
            }
          });
        } else {
          this.capexService.setFPCapexId(this.generalInfo.id);
        }
      }
      this.capexService.setCurrencyId(this.generalInfo.currencyId);
      this.dataNotIPA();
      this.layoutUtilsService.showActionNotification(this.translatePipe.transform(this.sendApprovalMessage), AlertType.Success);
      this.cdf.detectChanges();
    }, () => {
        this.ifError();
      })
    );
  }

  dataIfIPAExist() {
    this.subscriptionManager.add(
      this.ipaService.cancelIPA(this.userId, this.capexId ?? this.capexAPId).subscribe(() => {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA'), AlertType.Success);
        this.capexService.sendForApproval(this.capexId ?? this.capexAPId, this.userId).subscribe(() => {
          this.isSubmitting = false;
          if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
            if (this.summaryInfo) {
              this.capexService.saveSummaryInfo(this.summaryInfo).subscribe((res) => {
                if(res) {
                  this.capexService.setCapexId(this.generalInfo.id);
                }
              });
            } else {
              this.capexService.setCapexId(this.generalInfo.id);
            }
          } else {
            if (this.summaryInfo) {
              this.capexService.saveFPSummaryInfo(this.summaryInfo).subscribe((res) => {
                if(res) {
                  this.capexService.setFPCapexId(this.generalInfo.id);
                }
              });
            } else {
              this.capexService.setFPCapexId(this.generalInfo.id);
            }
          }
          this.capexService.setCurrencyId(this.generalInfo.currencyId);
          this.messagingService.setUnreadCountSubject(0);
          if (this.title !== TITLE.VP) {
            if (this.approvalStatus === STATUS.APPROVED && this.approverId === this.userId) {
              this.sendToCS = true;
            } else {
              this.isSubmitApprovalBtn = true;
              this.btnDisabled = true;
              this.isApproveOrReject = false;
              this.sendToCS = false;
              this.disableArchiveBtn = true;
            }
            this.capexService.setDisableCapexSubject(true);
            this.disableCAPEX = true;
          }
            this.layoutUtilsService.showActionNotification(this.sendApprovalMessage, AlertType.Success);
        }, () => {
          this.ifError();
      })
        this.cdf.detectChanges();
      }, () => {
      })
    );
  }

  ifError() {
    this.isSubmitApprovalBtn = false;
    this.cdf.detectChanges();
  }

  addApproveData(): ApproveOrRejectDTO {
    const rejectDTO = new ApproveOrRejectDTO();
    rejectDTO.reasonForRejection = null;
    rejectDTO.approved = true;
    rejectDTO.capexId = this.capexId ?? this.capexAPId;
    rejectDTO.userId = this.userId;
    return rejectDTO;
  }

  approveOrReject() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.APPROVE.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.APPROVE.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(
        this.capexService.approveOrReject(this.addApproveData()).subscribe(() => {
          this.isApproveOrReject = false;
          this.disableApproveBtn = true;
          if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
            this.capexService.setCapexId(this.capexId ?? this.capexAPId);
            this.capexService.setAuditIdSubject(this.capexAPId ?? this.capexId);
          } else {
            this.capexService.setFPCapexId(this.capexId ?? this.capexAPId);
            this.fpService.setFPAuditIdSubject(this.capexAPId ?? this.capexId);
          }
          this.layoutUtilsService.showActionNotification(this.sendApprovalMessage, AlertType.Success);
          this.messagingService.setUnreadCountSubject(0);
          this.cdf.detectChanges();
        }, () => {
          this.cdf.detectChanges();
        })
      );
    });
  }

  sendCS() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.SEND_CS.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.SEND_CS.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(
        this.capexService.sendToCS(this.capexId ?? this.capexAPId, this.userId).subscribe(() => {
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('DASHBOARD.SEND_CS_SUCCESS'), AlertType.Success);
          if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
            this.capexService.setAuditIdSubject(this.capexAPId ?? this.capexId);
            this.capexService.setCapexId(this.capexId ?? this.capexAPId);
          } else {
            this.fpService.setFPAuditIdSubject(this.capexAPId ?? this.capexId);
            this.capexService.setFPCapexId(this.capexId ?? this.capexAPId);
          }
          this.disableCSBtn = true;
          this.sendToCS = false;
          this.messagingService.setUnreadCountSubject(0);
          this.cdf.detectChanges();
        }, () => {
          this.cdf.detectChanges();
        })
      );
    });
  }

  closeCAPEX() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.ARCHIVE.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.ARCHIVE.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      if (this.approvalStatus === STATUS.PENDING_CS || this.approvalStatus === STATUS.REJECTED_CS) {
        this.subscriptionManager.add(
          this.capexService.closeCSCAPEX(this.capexId ?? this.capexAPId, this.userId).subscribe(() => {
            this.capexClose();
          }, () => {
            this.cdf.detectChanges();
          })
        );
      } else {
        this.subscriptionManager.add(
          this.capexService.closeCAPEX(this.capexId ?? this.capexAPId, this.userId).subscribe(() => {
            this.capexClose();
          }, () => {
            this.cdf.detectChanges();
          })
        );
      }
    });
  }

  capexClose() {
    this.layoutUtilsService.showActionNotification(this.translatePipe.transform('DASHBOARD.CLOSE_CAPEX'), AlertType.Success);
    this.disableArchiveBtn = true;
    this.disableApproveBtn = true;
    if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
      this.capexService.setAuditIdSubject(this.capexAPId ?? this.capexId);
      this.capexService.setCapexId(this.capexId ?? this.capexAPId);
    } else {
      this.fpService.setFPAuditIdSubject(this.capexAPId ?? this.capexId);
      this.capexService.setFPCapexId(this.capexId ?? this.capexAPId);
    }
    this.btnDisabled = true;
    this.capexService.setDisableCapexSubject(true);
    this.disableCAPEX = true;
    this.isSubmitApprovalBtn = false;
    this.messagingService.setUnreadCountSubject(0);
    this.cdf.detectChanges();
  }

  openRejectSidebar(data) {
    const params = { data: data, isEdit: data ? true : false };
    this.openSidebar.emit(params);
  }

  initializeForm(): FormGroup {
    return new FormGroup({
      deviationDate: new FormControl(this.currentDateUTC(), [Validators.required]),
      endNumber: new FormControl('', [Validators.required]),
      endName: new FormControl('', [Validators.required]),
      distributorName: new FormControl('', [Validators.required]),
      distributorNumber: new FormControl('', [Validators.required]),
      currentShipType: new FormControl('', [Validators.required]),
      nationalAccountId: new FormControl(''),
      nationalAccountName: new FormControl(''),
      naManager: new FormControl(''),
      isNA: new FormControl(false),
      storoPackTM: new FormControl(''),
      currency: new FormControl('', [Validators.required]),
      isGA: new FormControl(false)
    });
  }

  handleCurrency(event) {
    if(this.approvalStatus === STATUS.OPEN) {
      const _title = this.translatePipe.transform('CAPEX_WORKFLOW.CONFIRM_CURRENCY.TITLE');
      const _description = this.translatePipe.transform('CAPEX_WORKFLOW.CONFIRM_CURRENCY.BODY');

      const dialogRef = this.layoutUtilsService.submitElement(_title, _description);

      dialogRef.afterClosed().subscribe((res: boolean) => {
        if(res) {
          this.previousSelectedCurrency = event.value.id;
          this.currencyChange(event);
        } else {
          this.currency.setValue(this.currencyId ?? this.previousSelectedCurrency);
        }
      })
    } else {
      this.currencyChange(event);
    }
  }

  currencyChange(event) {
    if(event.value.id === CURRENCY.CAD) {
      this.showConversion = true;
      this.exchangeRate = event.value.exchangeRate;
      this.currencyId = CURRENCY.CAD;
    } else {
      this.showConversion = false;
      this.currencyId = CURRENCY.USD;
    }
    this.cdf.detectChanges();
    if(this.isFastLane) {
      if(this.route === APP_ROUTES.AP_PP_CAPEX || this.route.includes(APP_ROUTES.EDIT_AP_PP_CAPEX)) {
        this.subscriptionManager.add(
          this.capexService.setCurrency(this.capexId ?? this.capexAPId, this.currencyId).subscribe(() => {
            this.capexService.setCurrencyId(this.currencyId);
            this.capexService.setCapexId(this.capexId ?? this.capexAPId);
          })
        );
      } else {
        this.subscriptionManager.add(
          this.capexService.setFPCurrency(this.capexId ?? this.capexAPId, this.currencyId).subscribe(() => {
            this.capexService.setCurrencyId(this.currencyId);
            this.capexService.setFPCapexId(this.capexId ?? this.capexAPId);
          })
        );
      }
    }
    if (this.capexAPId || this.capexId) {
      this.currencyId = event.value.id;
    }
    if (this.generalInfoForm.valid) {
      this.handleGeneralInfo();
    }
  }

  addData(): GeneralInfo {
    const generalInfo = new GeneralInfo();
    generalInfo.capexLastModifiedDate = this.deviationDate.value;
    generalInfo.lastModifiedDate = this.currentDateUTC();
    generalInfo.priceGuaranteeTerm = null;
    generalInfo.shipToName = !this.isFastLane ? this.endName.value : '0';
    generalInfo.shipToNumber = !this.isFastLane ? this.endNumber.value : 0;
    generalInfo.soldToName = !this.isFastLane ? this.distributorName.value : '0';
    generalInfo.soldToNumber = !this.isFastLane ? this.distributorNumber.value : 0;
    generalInfo.shipToTypeEnum = !this.isFastLane ? this.currentShipType.value : 'NOT_APPLICABLE';
    generalInfo.id = this.id ? this.id : null;
    generalInfo.creationInfo.nationalAccountProgramId = this.isNA.value ? this.naId : null;
    generalInfo.creationInfo.nationalAccountManagerId = this.isNA.value ? this.namId : null;
    generalInfo.creationInfo.nationalAccountProgramName = this.isNA.value ? this.naName : null;
    generalInfo.creationInfo.nationalAccount = this.isFastLane ? false : this.isNA.value;
    generalInfo.creationInfo.globalAccount = this.isGA.value;
    generalInfo.creationInfo.previousVersionId = null;
    generalInfo.creationInfo.clonedFromId = null;
    generalInfo.creationInfo.version = AppConstants.versionNumber;
    if(this.isFastLane) {
      generalInfo.creationInfo.salesPersonId = null;
      generalInfo.creationInfo.approvalStatus = 'NOT_APPLICABLE';
    } else {
      generalInfo.creationInfo.salesPersonId = this.isTM ? +this.createdId : this.generalInfo.creationInfo.salesPersonId;
      generalInfo.creationInfo.approvalStatus = STATUS.OPEN;
    }
    generalInfo.creationInfo.createdById = +this.createdId;
    generalInfo.creationInfo.capexType = this.route === APP_ROUTES.AP_PP_CAPEX || this.route.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) ? ENTITY.AP_PP : ENTITY.FP;
    generalInfo.currencyId = this.currencyId;
    generalInfo.capexDate = this.currentDateUTC();
    generalInfo.fastLane = this.isFastLane;
    generalInfo.issId = !this.id ? +this.assignedIssId : null;
    generalInfo.priceGuaranteeTermApplied = this.applyPGT.value ?? false;
    return generalInfo;
  }

  setData(generalInfo: GeneralInfo) {
    this.generalInfoForm.setValue({
      deviationDate: generalInfo.capexLastModifiedDate,
      endName: generalInfo.shipToName,
      endNumber: generalInfo.shipToNumber,
      distributorName: generalInfo.soldToName,
      distributorNumber: generalInfo.soldToNumber,
      currentShipType: generalInfo.shipToTypeEnum,
      isNA: generalInfo.creationInfo.nationalAccount,
      nationalAccountName: generalInfo.creationInfo.nationalAccountProgramName,
      nationalAccountId: generalInfo.creationInfo.nationalAccountProgramNumber,
      naManager: generalInfo.creationInfo.nationalAccountManagerName,
      storoPackTM: generalInfo.creationInfo.salesPersonId,
      currency: generalInfo.currencyId,
      isGA: generalInfo.creationInfo.globalAccount
    });
    // this.onformValueChange();
  }

  handleGeneralInfo() {
    if (this.router.url === APP_ROUTES.AP_PP_CAPEX || this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX)) {
      if ((this.capexAPId || this.capexId) && this.generalInfoForm.valid) {
        this.capexService.setGeneralInfoDataSubject(this.addData());
      }
    } else {
      if ((this.capexAPId || this.capexId) && this.generalInfoForm.valid) {
        this.capexService.setFPGeneralInfoDataSubject(this.addData());
      }
    }
  }

  handleError(controls) {
    const invalidField = [];
    Object.keys(controls).forEach((controlName) =>
      controls[controlName].markAsTouched()
    );
    for (const name in controls) {
      if (controls[name].invalid) {
        invalidField.push(name);
        if(invalidField && invalidField.length) {
          this.layoutUtilsService.showActionNotification(
            this.translatePipe.transform('COMMON.ERROR_FIELDS') + Array.prototype.map.call(invalidField, (field) => field).join(", ") + '\n' + this.translatePipe.transform('COMMON.ERROR_DETAIL'), AlertType.Error);
        }
      }
    }
  }

  async submit() {
    const controls = this.generalInfoForm.controls;
    if (this.generalInfoForm.invalid) {
      this.handleError(controls);
      return;
    }
    this.getRegionId(this.regionId);
    if (this.summaryForm?.invalid) {
      this.handleError(controls);
      this.layoutUtilsService.showActionNotification(this.translatePipe.transform('COMMON.MISSING_ITEM'), AlertType.Error);
      return;
    }
    this.isSubmitting = true;
    if (this.route === APP_ROUTES.AP_PP_CAPEX || this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX)) {
      this.getApPpGeneralData();
    } else {
      this.getFpGeneralData();
    }
  }

  async getFpGeneralData() {
    if ((this.capexId || this.capexAPId) && !this.isFastLane) {
      this.subscriptionManager.add(
        this.capexService.setFPCurrency(this.capexId ?? this.capexAPId, this.currencyId).subscribe(() => {
          this.capexService.setCurrencyId(this.currencyId);
        })
      );
    }
    if ((this.capexId || this.capexAPId) && this.summaryInfo) {
      await this.capexService.saveFPSummaryInfo(this.summaryInfo).toPromise();
    }
    this.saveFPGeneralInfo();
  }

  saveFPGeneralInfo() {
    this.subscriptionManager.add(this.capexService.saveFPGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
      this.isSubmitting = false;
      this.generalInfo = res;
      localStorage.setItem('capexId', res.id.toString());
      if (this.capexId ?? this.capexAPId) {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CAPEX.GENERAL_INFO_UPDATE_SUCCESS'), AlertType.Success);
      } else {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CAPEX.GENERAL_INFO_SUCCESS'), AlertType.Success);
      }
      this.capexService.setFPCapexId(this.generalInfo.id);
      this.capexService.setCurrencyId(this.generalInfo.currencyId);
      this.ipaService.setSubmitStatusSubject(true);
      this.cdf.detectChanges();
    }, () => this.onError()));
  }

  async getApPpGeneralData() {
    if ((this.capexId || this.capexAPId) && !this.isFastLane) {
      this.subscriptionManager.add(
        this.capexService.setCurrency(this.capexId ?? this.capexAPId, this.currencyId).subscribe(() => {
          this.capexService.setCurrencyId(this.currencyId);
        })
      );
    }
    if ((this.capexId || this.capexAPId) && this.summaryInfo) {
      await this.capexService.saveSummaryInfo(this.summaryInfo).toPromise();
    }
    this.saveAPPPGeneralInfo();
  }

  saveAPPPGeneralInfo() {
    this.subscriptionManager.add(this.capexService.saveGeneralInfo(this.addData(), this.capexId ?? this.capexAPId, this.isFastLane).subscribe((res: GeneralInfo) => {
      this.isSubmitting = false;
      this.generalInfo = res;
      localStorage.setItem('capexId', res.id.toString());
      if (this.capexId ? this.capexId : this.capexAPId) {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CAPEX.GENERAL_INFO_UPDATE_SUCCESS'), AlertType.Success);
      } else {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('AP_PP_CAPEX.GENERAL_INFO_SUCCESS'), AlertType.Success);
      }
      this.capexService.setCapexId(this.generalInfo.id);
      this.capexService.setCurrencyId(this.generalInfo.currencyId);
      this.ipaService.setSubmitStatusSubject(true);
      this.cdf.detectChanges();
    }, () => this.onError()));
  }

  getAdminWorkflow(capexInfo) {
    if (capexInfo) {
      if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN) {
        this.isSubmitApprovalBtn = true;
        this.isApproveOrReject = false;
        this.btnDisabled = false;
        this.sendToCS = false;
      } else if ([STATUS.PENDING_APPROVAL_GLOBAL, STATUS.PENDING_APPROVAL, STATUS.PENDING_APPROVAL_VP].includes(capexInfo.approvalInfo.approvalStatus)) {
        this.isApproveOrReject = true;
        this.isSubmitApprovalBtn = false;
        this.disableApproveBtn = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_CS) {
        this.disableCSBtn = true;
        this.disableApproveBtn = false;
        this.disableArchiveBtn = false;
        this.sendToCS = false;
        this.isArchived = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED && !this.archived) {
        this.sendToCS = true;
        this.isSubmitApprovalBtn = false;
        this.isApproveOrReject = false;
        this.disableCSBtn = false;
      } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && !this.archived) {
        this.sendToCS = false;
        this.isSubmitApprovalBtn = true;
        this.capexService.setDisableCapexSubject(false);
        this.disableCAPEX = false;
        this.btnDisabled = false;
        this.isArchived = true;
        this.disableArchiveBtn = false;
        this.isApproveOrReject = false;
      } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && this.archived) {
        this.isSubmitApprovalBtn = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
        this.isArchived = false;
      }
    }
  }

  getCapexWorkflow(capexInfo) {
    if (capexInfo) {
      if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN && (this.userId === capexInfo.creationInfo.createdById || this.userId === capexInfo.creationInfo.salesPersonId || this.salesGroupId === capexInfo.creationInfo.salesGroupId)) {
        this.isSubmitApprovalBtn = true;
        this.isApproveOrReject = false;
        this.btnDisabled = false;
        this.sendToCS = false;
        this.isArchived = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_APPROVAL || this.salesGroupId === capexInfo.creationInfo.salesGroupId) {
        this.isSubmitApprovalBtn = false;
        this.btnDisabled = true;
        this.isApproveOrReject = false;
        this.sendToCS = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_CS || this.salesGroupId === capexInfo.creationInfo.salesGroupId) {
        this.disableCSBtn = true;
        this.sendToCS = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
        this.isSubmitApprovalBtn = false;
      }
      if (capexInfo.creationInfo.salesGroupId === this.salesGroupId && capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED) {
        this.sendToCS = true;
        this.isSubmitApprovalBtn = false;
        this.isApproveOrReject = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
      } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && !this.archived) {
        this.sendToCS = false;
        this.isSubmitApprovalBtn = true;
        this.capexService.setDisableCapexSubject(false);
        this.disableCAPEX = false;
        this.btnDisabled = false;
        this.isArchived = true;
        this.disableArchiveBtn = false;
        this.isApproveOrReject = false;
      } else if(([STATUS.REJECTED, STATUS.REJECTED_CS, STATUS.APPROVED].includes(capexInfo.approvalInfo.approvalStatus)) && this.archived) {
        this.isSubmitApprovalBtn = false;
        this.sendToCS = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
        this.isArchived = false;
      }
    }
  }

  getISSWorkflow(capexInfo) {
    if (capexInfo) {
      if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN) {
        this.isSubmitApprovalBtn = true;
        this.isApproveOrReject = false;
        this.btnDisabled = false;
        this.sendToCS = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_APPROVAL) {
        this.isSubmitApprovalBtn = true;
        this.btnDisabled = true;
        this.isApproveOrReject = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_CS) {
        this.disableCSBtn = true;
        this.sendToCS = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED) {
        this.sendToCS = true;
        this.isSubmitApprovalBtn = false;
        this.isApproveOrReject = false;
        this.disableCSBtn = false;
      } else if (capexInfo.approvalInfo.approvalStatus === STATUS.REJECTED_CS || capexInfo.approvalInfo.approvalStatus === STATUS.REJECTED) {
        this.isSubmitApprovalBtn = true;
        this.btnDisabled = false;
      }
    }
  }

  getTMWorkflow(capexInfo) {
    if ((capexInfo.creationInfo.createdById === this.userId || capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED) || (capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED && this.title === TITLE.ISS)) {
      this.sendToCS = true;
      this.isSubmitApprovalBtn = false;
      this.isApproveOrReject = false;
    }
  }

  getNAMWorkflow(capexInfo) {
    if ((capexInfo.creationInfo.createdById === this.userId || capexInfo.approvalInfo.approverId === this.userId) && capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED) {
      this.sendToCS = true;
      this.isSubmitApprovalBtn = false;
      this.isArchived = false;
    }
  }

  getGlobalWorkflow(capexInfo) {
    if (capexInfo.approvalInfo.approverId === this.userId && capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_APPROVAL_GLOBAL) {
      this.isApproveOrReject = true;
      this.isSubmitApprovalBtn = false;
    } else if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN) {
      this.isApproveOrReject = false;
      this.btnDisabled = true;
      this.sendToCS = false;
    } else if(capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_CS && !this.archived) {
      this.sendToCS = false;
      this.isSubmitApprovalBtn = false;
      this.isApproveOrReject = false;
      this.isArchived = false;
    } else if(capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED && !this.archived) {
      this.sendToCS = true;
      this.disableCSBtn = false;
    } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && !this.archived) {
      this.sendToCS = false;
      this.isSubmitApprovalBtn = true;
      this.capexService.setDisableCapexSubject(false);
      this.disableCAPEX = false;
      this.btnDisabled = false;
      this.isArchived = true;
      this.disableArchiveBtn = false;
      this.isApproveOrReject = false;
    } else if(([STATUS.REJECTED, STATUS.REJECTED_CS, STATUS.APPROVED].includes(capexInfo.approvalInfo.approvalStatus)) && this.archived) {
      this.isSubmitApprovalBtn = false;
      this.sendToCS = false;
      this.capexService.setDisableCapexSubject(true);
      this.disableCAPEX = true;
      this.isArchived = false;
    }
  }

  getRSMWorkflow(capexInfo) {
    if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_APPROVAL && this.userId === capexInfo.approvalInfo.approverId) {
      this.isApproveOrReject = true;
      this.isSubmitApprovalBtn = false;
    } else if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN) {
      this.isSubmitApprovalBtn = true;
      this.isApproveOrReject = false;
      this.btnDisabled = false;
      this.sendToCS = false;
    } else if (capexInfo.creationInfo.createdById === this.userId || capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED) {
      this.sendToCS = true;
      this.isSubmitApprovalBtn = false;
      this.isArchived = false;
      this.isApproveOrReject = false;
      this.disableCSBtn = false;
    }
    if (capexInfo.approvalInfo.approvalStatus === STATUS.PENDING_CS) {
      this.disableCSBtn = true;
      this.sendToCS = false;
      this.disableCAPEX = true;
      this.capexService.setDisableCapexSubject(true);
    } else if ([STATUS.PENDING_APPROVAL, STATUS.PENDING_APPROVAL_VP, STATUS.PENDING_APPROVAL_GLOBAL].includes(capexInfo.approvalInfo.approvalStatus)) {
      this.sendToCS = false;
      this.disableCAPEX = true;
      this.capexService.setDisableCapexSubject(true);
    } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && !this.archived) {
      this.sendToCS = false;
      this.isSubmitApprovalBtn = true;
      this.capexService.setDisableCapexSubject(false);
      this.disableCAPEX = false;
      this.btnDisabled = false;
      this.isArchived = true;
      this.disableArchiveBtn = false;
      this.isApproveOrReject = false;
    } else if(([STATUS.REJECTED, STATUS.REJECTED_CS, STATUS.APPROVED].includes(capexInfo.approvalInfo.approvalStatus)) && this.archived) {
      this.isSubmitApprovalBtn = false;
      this.sendToCS = false;
      this.capexService.setDisableCapexSubject(true);
      this.disableCAPEX = true;
      this.isArchived = false;
    }
  }

  getVPWorkflow(capexInfo) {
    if (capexInfo.approvalInfo.approverId === this.userId && ([STATUS.PENDING_APPROVAL, STATUS.PENDING_APPROVAL_VP].includes(capexInfo.approvalInfo.approvalStatus))) {
      this.isApproveOrReject = true;
    } else if (capexInfo.approvalInfo.approvalStatus === STATUS.OPEN) {
      this.isApproveOrReject = false;
      this.btnDisabled = true;
      this.sendToCS = false;
    } else if(([STATUS.REJECTED, STATUS.REJECTED_CS].includes(capexInfo.approvalInfo.approvalStatus)) && !this.archived) {
      this.sendToCS = false;
      this.isSubmitApprovalBtn = false;
      this.isApproveOrReject = false;
    } else if (capexInfo.approvalInfo.approvalStatus === STATUS.APPROVED && capexInfo.approvalInfo.approverId === this.userId && !this.archived) {
      this.sendToCS = true;
      this.disableCSBtn = false;
    }
  }

  getGeneralInfo(id: number): Promise<void> {
    return new Promise(resolve => {
      this.subscriptionManager.add(
        this.capexService.getGeneralInfo(id).subscribe((res) => {
          this.setData(res);
          this.capexService.setCurrencyId(res.currencyId);
          this.capexService.setGeneralInfoDataSubject(res);
          this.applyPGT.setValue(res.priceGuaranteeTermApplied);
          this.currencyId = res.currencyId;
          this.getSubmitAccess();
          this.getCPAList();
          this.getTM(res);
          this.subscriptionManager.add(
            this.capexService.getCapex(res.id).subscribe((res) => {
              this.nonEquipmentData = res.nonEquipmentMaterialInfoSection.pagedData.content;
              this.materialData = res.airPlusPaperPlusMaterialInfoSection.pagedData.content;
              this.equipmentData = res.airPlusPaperPlusEquipmentInfoSection.pagedData.content;
              this.archived = res.archived;
              this.approverId = res.approvalInfo.approverId;
              if (res.archived) {
                this.capexService.setDisableCapexSubject(true);
                this.disableCAPEX = true;
                this.disableArchiveBtn = true;
                this.isSubmitApprovalBtn = false;
              }
              this.isDeletedItem = res.generalInfoSection.itemDeleted;
              this.cdf.detectChanges();
            })
          );
          this.storoPackTM.setValue(res.creationInfo.salesPersonName);
          this.approvalStatus = res.approvalInfo.approvalStatus;
          this.generalInfo.creationInfo.salesPersonId = res.creationInfo.salesPersonId;
          this.capexService.setAuditIdSubject(this.generalInfo.id);
          this.capexColor = res?.approvalInfo?.color;
          this.name = res.creationInfo.salesPersonName;
          this.getCurrencyFieldAccess(res);
          this.getGeneralData(res);
          this.cdf.detectChanges();
          resolve();
        }, () => {
        }
        )
      );
    })
  }

  getCurrencyFieldAccess(res) {
    if(!this.isFastLane) {
      if((res.currencyId === CURRENCY.CAD || this.regionId === RegionId.CANADA_ID) && this.titleName !== this.titleConst.VIEWER_C1) {
        this.showConversion = true;
        this.exchangeRate = res.exchangeRate;
        this.currency.enable();
      } else {
        this.showConversion = false;
        this.currency.disable();
      }
    }
    this.currency.updateValueAndValidity();
  }

  getFPGeneralInfo(id: number) {
    this.subscriptionManager.add(
      this.capexService.getFPGeneralInfo(id).subscribe((res) => {
        this.setData(res);
        this.capexService.setCurrencyId(res.currencyId);
        this.capexService.setFPGeneralInfoDataSubject(res);
        this.applyPGT.setValue(res.priceGuaranteeTermApplied);
        this.currencyId = res.currencyId;
        this.getCPAList();
        this.getTM(res);
        this.subscriptionManager.add(
          this.capexService.getFPCapex(res.id).subscribe((res) => {
            this.materialData = res.foamPlusMaterialInfoSection.pagedData.content;
            this.equipmentData = res.foamPlusEquipmentInfoSection.pagedData.content;
            this.archived = res.archived;
            this.approverId = res.approvalInfo.approverId;
            if (res.archived) {
              this.capexService.setDisableCapexSubject(true);
              this.disableCAPEX = true;
              this.disableArchiveBtn = true;
              this.isSubmitApprovalBtn = false;
            }
            this.isDeletedItem = res.generalInfoSection.itemDeleted;
            this.cdf.detectChanges();
          })
        );
        this.approvalStatus = res.approvalInfo.approvalStatus;
        this.capexColor = res?.approvalInfo?.color;
        this.storoPackTM.setValue(res.creationInfo.salesPersonName);
        this.generalInfo.creationInfo.salesPersonId = res.creationInfo.salesPersonId;
        this.name = res.creationInfo.salesPersonName;
        this.fpService.setFPAuditIdSubject(this.generalInfo.id);
        this.getCurrencyFieldAccess(res);
        this.getGeneralData(res);
        this.cdf.detectChanges();
      }, () => {
      }
      )
    );
  }

  getTM(res) {
    if(this.salesPersonByTitle?.length) {
      this.salesPersonByTitle.forEach((data) => {
        if(res.creationInfo.salesPersonId === data.id) {
          this.regionId = data.regionId;
          this.getRegionId(this.regionId);
        }
      });
    }
  }

  getGeneralData(res) {
    switch (this.title) {
      case TITLE.TM: case TITLE.FPS:
        this.getTMWorkflow(res);
        this.getCapexWorkflow(res);
        break;
      case TITLE.ISS: case TITLE.ISS_GLOBAL: case TITLE.ISS_NATIONAL:
        this.getISSWorkflow(res);
        break;
      case TITLE.NAM:
        this.getCapexWorkflow(res);
        this.getNAMWorkflow(res);
        break;
      case TITLE.RSM: case TITLE.NSM:
        this.getRSMWorkflow(res);
        break;
      case TITLE.VP:
        this.getVPWorkflow(res);
        break;
      case TITLE.ADMIN: case TITLE.SUPPORT:
        this.getAdminWorkflow(res);
        break;
      case TITLE.VP_GLOBAL:
        this.getGlobalWorkflow(res);
        break;
    }
    if(this.title !== TITLE.ADMIN && this.title !== TITLE.SUPPORT) {
      if ((res.creationInfo.createdById === this.userId || res.creationInfo.salesPersonId === this.userId || res.creationInfo.salesGroupId === this.salesGroupId) && (res.approvalInfo.approvalStatus === STATUS.REJECTED && !this.archived)) {
        this.sendToCS = false;
        this.isSubmitApprovalBtn = true;
        this.capexService.setDisableCapexSubject(false);
        this.disableCAPEX = false
        this.btnDisabled = false;
        this.isArchived = true;
        this.isApproveOrReject = false;
      }
      if (this.title !== TITLE.CS && res.approvalInfo.approvalStatus === STATUS.APPROVED && this.archived) {
        this.sendToCS = false;
        this.isSubmitApprovalBtn = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true;
        this.btnDisabled = false;
        this.isArchived = false;
        this.isApproveOrReject = false;
      }
      if (res.approvalInfo.approvalStatus === STATUS.OPEN) {
        this.sendToCS = false;
      }
      if(res.approvalInfo.approvalStatus === STATUS.REJECTED_CS && this.title !== TITLE.CS && res.creationInfo.salesGroupId === this.salesGroupId) {
        this.capexService.setDisableCapexSubject(false);
        this.disableCAPEX = false
        this.btnDisabled = false;
        this.sendToCS = false;
        this.isApproveOrReject = false;
        this.isSubmitApprovalBtn = true;
      }
      if((res.approvalInfo.approvalStatus === STATUS.REJECTED || res.approvalInfo.approvalStatus === STATUS.REJECTED_CS) && this.archived) {
        this.sendToCS = false;
        this.isSubmitApprovalBtn = false;
        this.capexService.setDisableCapexSubject(true);
        this.disableCAPEX = true
        this.btnDisabled = false;
        this.isArchived = false;
        this.isApproveOrReject = false;
      }
    }
    if(res.approvalInfo.approvalStatus === STATUS.APPROVED && this.archived) {
      this.sendToCS = false;
      this.isSubmitApprovalBtn = false;
      this.isApproveOrReject = false;
    }
    if(this.role.includes(ROLE.ADMIN) && this.titleName === null) {
      this.disableCAPEX = true;
      this.capexService.setDisableCapexSubject(true);
    }
    if (res.approvalInfo.approvalStatus === STATUS.OPEN && this.titleName !== null) {
      this.capexService.setDisableCapexSubject(false);
      this.disableCAPEX = false;
      this.sendToCS = false;
    }
    if (this.titleName !== this.titleConst.VIEWER_C1) {
      if(res.creationInfo.globalAccount || [TITLE.ISS_GLOBAL, TITLE.ISS_NATIONAL].includes(this.title)) {
        this.isNA.disable();
        this.isGA.enable();
      } else if(res.creationInfo.nationalAccount || [TITLE.TM, TITLE.FPS, TITLE.ISS_GLOBAL, TITLE.ISS_NATIONAL].includes(this.title)) {
        this.isNA.enable();
        this.isGA.disable();
      } else {
        this.isNA.enable();
        this.isGA.enable();
      }
    }
    this.isNA.updateValueAndValidity();
    this.isGA.updateValueAndValidity();
    this.namId = res.creationInfo.nationalAccountManagerId;
    this.naName = res.creationInfo.nationalAccountProgramName;
    this.naId = res.creationInfo.nationalAccountProgramId;
    if (this.isNA.value || res.creationInfo.nationalAccount) {
      this.nationalAccountName.setValidators(Validators.required);
    } else {
      this.nationalAccountName.clearValidators();
    }
    this.nationalAccountName.updateValueAndValidity();
    this.setRevertAction(res);
    if(this.currencyId === CURRENCY.CAD) {
      this.showConversion = true;
      this.exchangeRate = this.currencyExchangeRate;
    } else {
      this.showConversion = false;
    }
  }

  setRevertAction(res: GeneralInfo) {
    if([TITLE.ADMIN, TITLE.SUPPORT, TITLE.RSM, TITLE.NSM, TITLE.ISS].includes(this.title) || res.creationInfo.createdById === this.userId || res.creationInfo.salesPersonId === this.userId || res.creationInfo.salesGroupId === this.salesGroupId) {
      if([STATUS.PENDING_APPROVAL, STATUS.APPROVED, STATUS.PENDING_CS, STATUS.PENDING_APPROVAL_GLOBAL, STATUS.PENDING_APPROVAL_VP, STATUS.NOT_APPLICABLE].includes(res.approvalInfo.approvalStatus)) {
        this.revertBtn = true;
      }
    }
    this.subscriptionManager.add(
      this.capexService.getChangedColor().subscribe((res: string) => {
        this.capexColor = res;
        this.cdf.detectChanges();
      })
    );
  }

  revertStatus() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.REVERT.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.REVERT.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      // on confirm revert we need to set isValueEdit = false and we will then not show cancel CPA prompt to user until user change any value
      this.isValueEdit = false;
      this.subscriptionManager.add(
        this.capexService.revertCAPEXStatus(this.userId, this.capexId ?? this.capexAPId).subscribe(() => {
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('CAPEX_WORKFLOW.REVERT.SUCCESS'), AlertType.Success);
          this.revertBtn = false;
          if (this.router.url.includes(APP_ROUTES.EDIT_AP_PP_CAPEX) || this.router.url === APP_ROUTES.AP_PP_CAPEX) {
            this.capexService.setAuditIdSubject(this.capexAPId ?? this.capexId);
            this.capexService.setCapexId(this.capexId ?? this.capexAPId);
          } else {
            this.fpService.setFPAuditIdSubject(this.capexAPId ?? this.capexId);
            this.capexService.setFPCapexId(this.capexId ?? this.capexAPId);
          }
          this.disableCAPEX = false;
          this.capexService.setDisableCapexSubject(false);
          this.cdf.detectChanges();
        }, () => {
          this.cdf.detectChanges();
        })
      );
    });
  }

  getCurrency() {
    this.subscriptionManager.add(
      this.capexService.getCurrency().subscribe((res: Currency[]) => {
        this.currencyList = res;
      }, () => {
      })
    );
  }

  getSalesPerson() {
    let titleId: number;
    if (this.route === APP_ROUTES.AP_PP_CAPEX || this.route.includes(APP_ROUTES.EDIT_AP_PP_CAPEX)) {
      titleId = TitleId.TM;
    } else {
      titleId = TitleId.FPS;
    }
    if (this.titleName === TITLE.NAM && this.router.url.includes(APP_ROUTES.AP_PP_CAPEX)) {
      this.subscriptionManager.add(
        this.userService.getManagerByTitle(titleId).subscribe((res: User[]) => {
          this.getTMData(res);
        })
      );
    } else if (this.titleName === TITLE.ISS && this.router.url.includes(APP_ROUTES.AP_PP_CAPEX)) {
      this.subscriptionManager.add(
        this.capexService.getRegionISS(this.createdId, titleId).subscribe((res: User[]) => {
          this.getTMData(res);
        })
      );
    } else if(this.router.url.includes(APP_ROUTES.FP_CAPEX) && ([TITLE.RSM].includes(this.title))) {
      this.subscriptionManager.add(
        this.userService.getManagerByTitleAndRegion(TitleId.TM, this.regionId).subscribe((res: User[]) => {
          this.getTMData(res);
        })
      );
    } else if(this.title === TITLE.NSM || ([TITLE.ADMIN, TITLE.SUPPORT, TITLE.NAM, TITLE.VP, TITLE.VP_GLOBAL, TITLE.ISS_GLOBAL, TITLE.ISS_NATIONAL].includes(this.title) && this.router.url.includes(APP_ROUTES.FP_CAPEX))) {
      this.subscriptionManager.add(
        this.capexService.getAllTMAndFPS().subscribe((res: User[]) => {
          this.getTMData(res);
        })
      );
    } else if(this.router.url.includes(APP_ROUTES.FP_CAPEX) && this.title === TITLE.ISS) {
        this.subscriptionManager.add(
          this.regionBaseIssId()
        );
      } else {
        this.subscriptionManager.add(
          this.userService.getManagerByTitleAndRegion(titleId, this.regionId).subscribe((res: User[]) => {
            this.getTMData(res);
           })
        );
      }
    }

    regionBaseIssId() {
      this.capexService.getRegionBaseIssid('foamplus').subscribe((res) =>{
        if (res === this.userId) {
          this.capexService.getAllTMAndFPS().subscribe((res: User[]) => {
            this.isTMlistShown = true;
            this.getTMData(res);
          })
        } else {
          this.capexService.getAllFPSByIss(this.userId).subscribe((res: User[]) => {
            this.getTMData(res);
          })
        }
      })
    }

  getTMData(res) {
    this.salesPersonByTitle = res;
    this.tmList = res;
    this.cdf.detectChanges();
  }

  getAllNationalAccount() {
    this.loading$.next(true);
    this.setUserFilter();
    this.subscriptionManager.add(this.masterDataService.getDataByFilter(this.filter).subscribe((nationalAccount: NationalAccount[]) => {
      this.loading$.next(false);
      this.nationalAccountList = nationalAccount;
      this.namList = nationalAccount;
      this.filter = new FilterObject();
      this.cdf.detectChanges();
    }, () => {
      this.loading$.next(false);
      this.filter = new FilterObject();
      this.cdf.detectChanges();
    }));
  }

  setUserFilter() {
    this.filter.operator = FILTER_OPERATOR.NOOP;
    this.filter.entity = ENTITY.NATIONAL_ACCOUNT;
    this.filter.left = null;
    this.filter.right = null;
    const naFilter: Filter = new Filter();
    naFilter.dataType = AppConstants.longDataType;
    naFilter.key = 'nationalAccountManager.id';
    naFilter.operator = AppConstants.eq;
    naFilter.value = this.createdId.toString();
    if (this.titleName === TITLE.NAM) {
      this.filter.value.push(naFilter);
    } else {
      this.filter.value = null;
    }
    this.filter.orderBy = [];
  }

  handleAccount(event) {
    this.setvalueEdit();
    if (this.nationalAccountName.value) {
      this.nationalAccountId.setValue(event.value.number);
      this.naManager.setValue(event.value.nationalAccountManagerName);
      this.naId = event.value.id;
      this.namId = event.value.nationalAccountManagerId;
      this.naName = event.value.name;
      this.nationalAccountName.setValue(event.value.name);
    }
    if (this.generalInfoForm.valid) {
      this.handleGeneralInfo();
    }
  }

  objectCompare(option, value) {
    if (option && value) {
      return option.name === value;
    }
  }

  currencyCompare(option, value) {
    if (option && value) {
      return option.id === value;
    }
  }

  setvalueEdit(){
    this.isValueEdit = true;
  }
  handleNA(event) {
    this.setvalueEdit();
    if (!event.checked) {
      this.nationalAccountId.setValue('');
      this.nationalAccountName.setValue('');
      this.naManager.setValue('');
      this.nationalAccountName.clearValidators();
      this.isGA.enable();
    } else {
      this.nationalAccountName.setValidators([Validators.required]);
      this.isGA.disable();
    }
    if([TITLE.TM, TITLE.FPS].includes(this.title)) {
      this.isGA.disable();
    }
    this.isGA.updateValueAndValidity();
    this.nationalAccountName.updateValueAndValidity();
  }

  handleGA(event) {
    this.setvalueEdit();
    if(event.checked) {
      this.isNA.disable();
    } else {
      this.isNA.enable();
    }
    this.isNA.updateValueAndValidity();
  }

  onError() {
    this.isSubmitting = false;
    this.cdf.detectChanges();
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.generalInfoForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }

  // No more use this method -
  onformValueChange() {
    this.subscriptionManager.add(
      this.generalInfoForm.valueChanges.pipe(
        startWith(this.generalInfoForm.value),
        pairwise(),
        map(([oldValues, newValues]) => {
          return Object.keys(newValues).find(k => newValues[k] != oldValues[k]);
        }),
      ).subscribe(key => {
        if(!!key && key !== undefined && key !== null){
          if (!['currency', 'deviationDate', 'distributorNumber', 'endNumber', 'distributorName', 'endName', 'currentShipType'].includes(key)) {
            // this.isValueEdit = true;
          }
        }
      })
    )
  }

}
