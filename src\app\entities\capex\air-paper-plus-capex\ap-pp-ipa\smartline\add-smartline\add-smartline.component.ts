import { Component, OnInit, EventEmitter, Output, Input, ChangeDetectorRef, SimpleChanges, OnChanges } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Smartline } from '@shared/models/ipa.model';
import { AppConstants, INTEGRATION_TYPE } from '@shared/constants';
import { TranslatePipe } from '@ngx-translate/core';
import { ApPpIpaService } from '../../ap-pp-ipa.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';

@Component({
  selector: 'app-add-smartline',
  templateUrl: './add-smartline.component.html',
  styleUrls: ['./add-smartline.component.scss']
})
export class AddSmartlineComponent extends SflBaseComponent implements OnInit, OnChanges {
  @Output() closeSidebarEvent: EventEmitter<boolean> = new EventEmitter();
  @Input() sidebarParams: SidebarParams<Smartline>;
  @Input() versionID: number;
  smartlineTitle: string;
  smartlineForm: FormGroup = this.initializeForm();
  upperRackBox = this.smartlineForm.controls['upperRackBox'];
  upperRack = this.smartlineForm.controls['upperRack'];
  tableType = this.smartlineForm.controls['tableType'];
  noOfDividers = this.smartlineForm.controls['noOfDividers'];
  distanceBetweenTables = this.smartlineForm.controls['distanceBetweenTables'];
  upperShelf = this.smartlineForm.controls['upperShelf'];
  tableHeightAuto = this.smartlineForm.controls['tableHeightAuto'];
  cableMngmt = this.smartlineForm.controls['cableMngmt'];
  hopperType = this.smartlineForm.controls['hopperType'];
  lift = this.smartlineForm.controls['lift'];
  lowerShelf = this.smartlineForm.controls['lowerShelf'];
  dividers = this.smartlineForm.controls['dividers'];
  drawersReqd = this.smartlineForm.controls['drawersReqd'];
  noOfDrawers = this.smartlineForm.controls['noOfDrawers'];
  moniterArm = this.smartlineForm.controls['moniterArm'];
  hopperSizeL = this.smartlineForm.controls['hopperSizeL'];
  hopperSizeW = this.smartlineForm.controls['hopperSizeW'];
  hopperSizeH = this.smartlineForm.controls['hopperSizeH'];
  tableTopL = this.smartlineForm.controls['tableTopL'];
  tableTopW = this.smartlineForm.controls['tableTopW'];
  tableTopH = this.smartlineForm.controls['tableTopH'];
  scaleRecessL = this.smartlineForm.controls['scaleRecessL'];
  scaleRecessW = this.smartlineForm.controls['scaleRecessW'];
  scaleRecessH = this.smartlineForm.controls['scaleRecessH'];
  upperShelfPrinter = this.smartlineForm.controls['upperShelfPrinter'];
  lowerShelfPrinter = this.smartlineForm.controls['lowerShelfPrinter'];
  notes = this.smartlineForm.controls['notes'];
  noOfPackTable = this.smartlineForm.controls['noOfPackTable'];
  rightLeftVersion = this.smartlineForm.controls['rightLeftVersion'];
  scaleRecess = this.smartlineForm.controls['scaleRecess'];
  remoteDisplay = this.smartlineForm.controls['remoteDisplay'];
  decimalPattern = AppConstants.decimalNumberPatternWithZero;
  numberPattern = AppConstants.allowZeroPattern;
  smartlineDTO: Smartline = new Smartline();
  userId = +localStorage.getItem('userId');
  tableTypeList = ['NA', 'SINGLE_TABLE_I', 'SINGLE_TABLE_II', 'SINGLE_TABLE_III', 'SINGLE_TABLE_IV', 'DOUBLE_TABLE_II', 'DOUBLE_TABLE_III', 'DOUBLE_TABLE_IV'];
  smartlineId: number;
  constructor(
    private readonly translatePipe: TranslatePipe,
    private readonly cdf: ChangeDetectorRef,
    private readonly ipaService: ApPpIpaService,
    private readonly layoutUtilsService: LayoutUtilsService) {
    super();
  }

  ngOnInit(): void {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
    this.setComponentTitle();
    if (simpleChanges.sidebarParams && simpleChanges.sidebarParams.currentValue) {
      if (this.sidebarParams.isEdit) {
        this.smartlineId = this.sidebarParams.data.id;
      }
      this.getSmartlineDetail();
    }
  }

  setComponentTitle() {
    this.smartlineTitle = this.translatePipe.transform('IPA.SMARTLINE.TITLE');
  }

  handlePackTable(event) {
    if(event.target.value === "1") {
      this.rightLeftVersion.disable();
      this.distanceBetweenTables.disable();
    } else {
      this.rightLeftVersion.enable();
      this.distanceBetweenTables.enable();
    }
    this.rightLeftVersion.updateValueAndValidity();
    this.distanceBetweenTables.updateValueAndValidity();
  }

  getSmartlineDetail() {
    if (this.smartlineId) {
      this.subscriptionManager.add(
        this.ipaService.getSmartlineById(this.smartlineId).subscribe((res: Smartline) => {
          this.smartlineDTO = res;
          if(res.numberOfPackTables === 1) {
            this.rightLeftVersion.disable();
            this.distanceBetweenTables.disable();
          } else {
            this.rightLeftVersion.enable();
            this.distanceBetweenTables.enable();
          }
          this.rightLeftVersion.updateValueAndValidity();
          this.distanceBetweenTables.updateValueAndValidity();
          this.setDataToForm(this.smartlineDTO);
          this.loading$.next(false);
          this.cdf.detectChanges();
        }, () => this.loading$.next(false))
      );
    } else {
      this.loading$.next(false);
    }
  }

  initializeForm(): FormGroup {
    return new FormGroup({
      upperRackBox: new FormControl(''),
      upperRack: new FormControl(''),
      tableType: new FormControl(''),
      noOfDividers: new FormControl(''),
      distanceBetweenTables: new FormControl('', [Validators.required]),
      upperShelf: new FormControl(''),
      tableHeightAuto: new FormControl(''),
      cableMngmt: new FormControl(''),
      hopperType: new FormControl(''),
      lowerShelf: new FormControl(''),
      lift: new FormControl(''),
      dividers: new FormControl(''),
      drawersReqd: new FormControl(''),
      noOfDrawers: new FormControl(''),
      moniterArm: new FormControl(''),
      hopperSizeL: new FormControl(''),
      hopperSizeW: new FormControl(''),
      hopperSizeH: new FormControl(''),
      tableTopL: new FormControl(''),
      tableTopW: new FormControl(''),
      tableTopH: new FormControl(''),
      scaleRecessL: new FormControl(''),
      scaleRecessW: new FormControl(''),
      scaleRecessH: new FormControl(''),
      upperShelfPrinter: new FormControl(''),
      lowerShelfPrinter: new FormControl(''),
      notes: new FormControl('', [Validators.required]),
      noOfPackTable: new FormControl(''),
      rightLeftVersion: new FormControl(''),
      scaleRecess: new FormControl(''),
      remoteDisplay: new FormControl('')
    });
  }

  addData(): Smartline {
    const smartline = new Smartline();
    smartline.additionalNotes = this.notes.value ? this.notes.value : null;
    smartline.cableManagement = this.cableMngmt.value ? this.cableMngmt.value : false;
    smartline.distanceBetweenTables = this.distanceBetweenTables.value ? this.distanceBetweenTables.value : 0;
    smartline.drawersRequiredUnderTableTop = this.drawersReqd.value ? this.drawersReqd.value : false;
    smartline.hopperSize.length = this.hopperSizeL.value ? this.hopperSizeL.value : 0;
    smartline.hopperSize.width = this.hopperSizeW.value ? this.hopperSizeW.value : 0;
    smartline.hopperSize.height = this.hopperSizeH.value ? this.hopperSizeH.value : 0;
    smartline.hopperType = this.hopperType.value;
    smartline.id = this.smartlineId ? this.smartlineId : null;
    smartline.integrationType = INTEGRATION_TYPE.SMARTLINE;
    smartline.ipaVersionId = this.versionID;
    smartline.liftOrVenturi = this.lift.value;
    smartline.lowerMidLevelShelf = this.lowerShelf.value ? this.lowerShelf.value : false;
    smartline.monitorOrKeyboardArm = this.moniterArm.value ? this.moniterArm.value : false;
    smartline.numberOfDividersOnShelf = this.dividers.value ? +this.dividers.value : 0;
    smartline.numberOfDividersOnUpperRack = this.noOfDividers.value ? +this.noOfDividers.value : 0;
    smartline.numberOfDrawers = this.noOfDrawers.value ? this.noOfDrawers.value : 0;
    smartline.numberOfPackTables = this.noOfPackTable.value ? this.noOfPackTable.value : 0;
    smartline.remoteScaleDisplay = this.remoteDisplay.value ? this.remoteDisplay.value : false;
    smartline.rightAndLeftHandVersion = this.rightLeftVersion.value ? this.rightLeftVersion.value : false;
    smartline.scaleRecess = this.scaleRecess.value ? this.scaleRecess.value : false;
    smartline.scaleRecessSize.length = this.scaleRecessL.value ? this.scaleRecessL.value : 0;
    smartline.scaleRecessSize.width = this.scaleRecessW.value ? this.scaleRecessW.value : 0;
    smartline.scaleRecessSize.height = this.scaleRecessH.value ? this.scaleRecessH.value : 0;
    smartline.tableHeightAutoAdjustable = this.tableHeightAuto.value ? this.tableHeightAuto.value : false;
    smartline.tableTopSize.length = this.tableTopL.value ? this.tableTopL.value : 0;
    smartline.tableTopSize.width = this.tableTopW.value ? this.tableTopW.value : 0;
    smartline.tableTopSize.height = this.tableTopH.value ? this.tableTopH.value : 0;
    smartline.tableType = this.tableType.value;
    smartline.upperRack = this.upperRack.value ? this.upperRack.value : false;
    smartline.upperRackBoxDividers = this.upperRackBox.value ? this.upperRackBox.value : false;
    smartline.upperShelf = this.upperShelf.value ? this.upperShelf.value : false;
    smartline.upperShelfPrinterPullout = this.upperShelfPrinter.value ? this.upperShelfPrinter.value : false;
    smartline.lowerShelfPrinterPullout = this.lowerShelfPrinter.value ? this.lowerShelfPrinter.value : false;
    return smartline;
  }

  setDataToForm(dto: Smartline) {
    this.smartlineForm.setValue({
      upperRackBox: dto.upperRackBoxDividers,
      upperRack: dto.upperRack,
      tableType: dto.tableType,
      noOfDividers: dto.numberOfDividersOnUpperRack,
      distanceBetweenTables: dto.distanceBetweenTables,
      upperShelf: dto.upperShelf,
      tableHeightAuto: dto.tableHeightAutoAdjustable,
      cableMngmt: dto.cableManagement,
      hopperType: dto.hopperType,
      lowerShelf: dto.lowerMidLevelShelf,
      lift: dto.liftOrVenturi,
      dividers: dto.numberOfDividersOnShelf,
      drawersReqd: dto.drawersRequiredUnderTableTop,
      noOfDrawers: dto.numberOfDrawers,
      moniterArm: dto.monitorOrKeyboardArm,
      hopperSizeL: dto.hopperSize.length,
      hopperSizeW: dto.hopperSize.width,
      hopperSizeH: dto.hopperSize.height,
      tableTopL: dto.tableTopSize.length,
      tableTopW: dto.tableTopSize.width,
      tableTopH: dto.tableTopSize.height,
      scaleRecessL: dto.scaleRecessSize.length,
      scaleRecessW: dto.scaleRecessSize.width,
      scaleRecessH: dto.scaleRecessSize.height,
      upperShelfPrinter: dto.upperShelfPrinterPullout,
      notes: dto.additionalNotes,
      noOfPackTable: dto.numberOfPackTables,
      rightLeftVersion: dto.rightAndLeftHandVersion,
      scaleRecess: dto.scaleRecess,
      remoteDisplay: dto.remoteScaleDisplay,
      lowerShelfPrinter: dto.lowerShelfPrinterPullout
    });
  }

  submit() {
    const controls = this.smartlineForm.controls;
    if (this.smartlineForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      return;
    }
    this.isSubmitting = true;
    this.subscriptionManager.add(this.ipaService.addSmartline(this.addData(), this.userId).subscribe((res: Smartline) => {
      this.isSubmitting = false;
      this.smartlineDTO = res;
      if (this.smartlineId) {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.SMARTLINE.SUCCESS_UPDATE'), AlertType.Success);
      } else {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.SMARTLINE.SUCCESS_ADD'), AlertType.Success);
      }
      this.ipaService.setDataSubject(res.ipaVersionId);
      this.closeSidebar();
      this.cdf.detectChanges();
    }, () => this.onError()));
  }

  onError() {
    this.isSubmitting = false;
    this.cdf.detectChanges();
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.smartlineForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }

  closeSidebar() {
    this.closeSidebarEvent.next(true);
  }

}
