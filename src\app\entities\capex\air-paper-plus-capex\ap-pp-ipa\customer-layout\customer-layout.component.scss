.mat-expansion-panel {
  border: 1px solid #e6ebef !important;
}

.mat-expansion-panel:not([class*="mat-elevation-z"]) {
  box-shadow: none !important;
}

::ng-deep.mat-expansion-panel-body {
  padding: 0 !important;
}

.mat-expansion-panel-header.mat-expanded {
  height: 48px !important;
}

.mat-expansion-panel-header-description {
  flex-grow: 0 !important;
}

::ng-deep.card-label {
  color: #073968 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.mat-card.scrollable-content {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mat-card.scrollable-content mat-card-content {
  overflow-y: auto;
}
.mat-card.scrollable-content mat-card-title {
  display: block;
}

.form-label {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 500;
  margin-top: 25px;
}

@media (min-width: 900px) {
  .form-label {
    text-align: right;
  }

  .main-title {
    float: right;
  }
}

@media (max-width: 900px) {
  .form-label {
    text-align: left;
    margin-top: 0 !important;
  }

  .main-title {
    text-align: left;
  }

  .lwh-title {
    text-align: left !important;
  }
}

.main-title, .lwh-title {
  height: 20px;
  color: #1a1a1a;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
}

.title-content {
  margin-top: 25px;
}

::ng-deep.mat-form-field-appearance-legacy .mat-form-field-wrapper {
  padding-bottom: 0.85em !important;
}

.input-group-text {
  height: 40px !important;
}

.field-box {
  height: 40px !important;
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
  padding: 0 0.25em 0 0.75em !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  border: 1px solid #d0d7e1 !important;
  border-radius: 2px !important;
}

::ng-deep.mat-input-element {
  caret-color: #d0d7e1 !important;
}

::ng-deep.mat-form-field-appearance-fill .mat-form-field-flex,
.mat-form-field-appearance-fill:focus {
  background-color: #f3f6f9 !important;
  color: #1a1a1acf !important;
}

.radio-grp {
  padding-top: 2rem;
}
