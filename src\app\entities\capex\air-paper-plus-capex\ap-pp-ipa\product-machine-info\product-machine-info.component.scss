.open-table {
  padding-left: 40px;
  padding-right: 40px;
}

.table-responsive {
  padding: 25px;
}

thead.tableHeader {
  background-color: #ffffff !important;
}

.deleted-color {
  color: #e40c0c;
}

.table-border {
  border: 1px solid #ebedf3;
}

::ng-deep.mat-select-arrow-wrapper {
  padding-top: 0.8rem !important;
}

.tab-title {
  box-sizing: border-box;
  height: 30px;
  border: 1px solid #D0D7E1;
  background-color: #F3F5F8;
  text-align: center;
  line-height: 25px;
}

.system-text {
  color: #C40E0E !important;
}

.footer-text {
  color: #1a1a1a;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
}

.hopper-label {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.hopper-text {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  text-align: center;
}

.main-title {
  height: 20px;
  color: #1a1a1a;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
}

.overhead-title {
  line-height: 35px;
}

.add-btn {
  height: 35px;
}

.no-pic {
  color: #949494;
  font-size: 16px;
  font-weight: 500;
}

.img-size {
  width: 250px;
  height: 210px;
}

.caption-class {
  margin-top: 1rem;
  font-size: 14px;
  font-weight: 500;
  margin-left: 1rem;
  word-break: break-word;
  width: 250px;
}

.img-wrap {
  position: relative;
}

.img-wrap .close {
  position: absolute;
  right: 10px;
  z-index: 100;
  top: 7px;
  background: #ffffff;
  border-radius: 50%;
  padding: 3px;
  cursor: pointer;
  padding-left: 6px;
  padding-right: 6px;
  opacity: 1;
}

.close {
  color: #1a1a1a !important;
  float: right;
  font-size: 15px;
  font-weight: bold;
}