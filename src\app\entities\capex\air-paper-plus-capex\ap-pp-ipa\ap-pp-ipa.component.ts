import { Component, OnInit, ChangeDetectorRef, Input, AfterViewInit } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ApPpIpaService } from './ap-pp-ipa.service';
import { IPA, OverheadHopper, UndertableCart, Smartline, ConceptSAMS, IPAVersion, VersionListPagedData, Integration } from '@shared/models/ipa.model';
import { AppConstants, TITLE, STATUS, ROLE } from '@shared/constants';
import { TitleId } from '@shared/models/filter.model';
import { UserService } from '@entities/user-management/user.service';
import { User } from '@shared/models/user.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { TranslatePipe } from '@ngx-translate/core';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { MatSidenav } from '@angular/material/sidenav';
import { PageableQuery } from '@shared/services/utils';
import { saveAs } from 'file-saver';
import { Router } from '@angular/router';

@Component({
  selector: 'app-ap-pp-ipa',
  templateUrl: './ap-pp-ipa.component.html',
  styleUrls: ['./ap-pp-ipa.component.scss']
})
export class ApPpIpaComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  panelOpenState: boolean;
  customIntegrationForm: FormGroup = this.initializeForm();
  tm = this.customIntegrationForm.controls['tm'];
  requestDate = this.customIntegrationForm.controls['requestDate'];
  nam = this.customIntegrationForm.controls['nam'];
  rsm = this.customIntegrationForm.controls['rsm'];
  fst = this.customIntegrationForm.controls['fst'];
  projectNumber = this.customIntegrationForm.controls['projectNumber'];
  designEngineer = this.customIntegrationForm.controls['designEngineer'];
  companyName = this.customIntegrationForm.controls['companyName'];
  companyAddress = this.customIntegrationForm.controls['companyAddress'];
  companyCity = this.customIntegrationForm.controls['companyCity'];
  companyState = this.customIntegrationForm.controls['companyState'];
  companyZip = this.customIntegrationForm.controls['companyZip'];
  companyCountry = this.customIntegrationForm.controls['companyCountry'];
  companyContact = this.customIntegrationForm.controls['companyContact'];
  companyPhone = this.customIntegrationForm.controls['companyPhone'];
  totalCost = this.customIntegrationForm.controls['totalCost'];
  country = ['US', 'CANADA'];
  ipaDTO: IPA = new IPA();
  numberPattern = AppConstants.numberPattern;
  salesPersonByTitle: User[] = [];
  createdBY: string;
  createdOn: Date;
  versionID: number;
  version: number;
  id: number;
  companyAddressId: number;
  ipaStatus: string;
  rsmId: number;
  tmId: number;
  namId: number;
  @Input() userId: number;
  @Input() capexId: number;
  @Input() currencyId: number;
  @Input() salesPersonId: number;
  isOverheadHopperVisible = false;
  overheadHopperSidebarParams: SidebarParams<OverheadHopper>;
  underTableCartSidebarParams: SidebarParams<UndertableCart>;
  isUndercartVisible: boolean;
  smartlineSidebarParams: SidebarParams<Smartline>;
  isSmartlineVisible: boolean;
  SAMSidebarParams: SidebarParams<ConceptSAMS>;
  isSAMVisible: boolean;
  versionLoading: boolean;
  versionOpen: boolean;
  totalItems: number;
  pageableQuery: PageableQuery = new PageableQuery();
  viewPageableObj: PageableQuery = new PageableQuery();
  totalElements: number;
  totalPaginationObj: PageableQuery = new PageableQuery();
  activeReverse: boolean;
  versionList: IPAVersion[] = [];
  isApproving: boolean;
  isIntegration: boolean;
  title = localStorage.getItem('titleName');
  closeEnabled = true;
  isCancelling: boolean;
  showTotalCost: boolean;
  titleConst = TITLE;
  titleName = localStorage.getItem('titleName');
  status = STATUS;
  roles = localStorage.getItem('roles');
  isAdmin: boolean;
  submitAccess: boolean;
  isReverting: boolean;
  isIPA: any;
  isOpenIPA: any;

  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly ipaService: ApPpIpaService,
    private readonly userService: UserService,
    private readonly translatePipe: TranslatePipe,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly router: Router
  ) {
    super();
  }


  ngAfterViewInit(): void {
    if (this.title === this.titleConst.VIEWER_C1) {
      this.customIntegrationForm.disable();
      this.tm.disable();
      this.requestDate.disable();
      this.nam.disable();
    }
  }

  checkIfFieldsNeedsToBeDisabled() {
    if ((this.title === this.titleConst.IPA && this.ipaStatus === 'OPEN')) {
      this.designEngineer.disable();
      this.projectNumber.disable();
      this.fst.disable();
      this.totalCost.disable();
      this.companyName.disable();
      this.companyContact.disable();
      this.companyAddress.disable();
      this.companyCity.disable();
      this.companyState.disable();
      this.companyCountry.disable();
      this.companyZip.disable();
      this.companyPhone.disable();
    }
  }

  ngOnInit(): void {
    this.subscriptionManager.add(
      this.ipaService.getIPASubject().subscribe((res: IPA) => {
        if (res) {
          this.ipaDTO = res;
          this.setDataToForm(res);
          this.createdBY = res.createdByName;
          this.createdOn = res.createdOn;
          this.versionID = res.ipaVersionId;
          this.version = res.version;
          this.id = res.id ? res.id : null;
          this.companyAddressId = res.companyAddress.id;
          this.ipaStatus = res.ipaStatus;
          this.version = res.version;
          this.rsmId = res.regionalSalesManagerId;
          this.tmId = res.territoryManagerId;
          this.namId = res.nationalAccountManagerId;
          this.setNoneOption(res);
          this.getAllNationalAccount();
          this.getAllIntegration();
          this.subscriptionManager.add(
            this.ipaService.getDeletedItem().subscribe((res: boolean) => {
              if (res) {
                this.layoutUtilsService.showActionNotification(this.translatePipe.transform('COMMON.DELETED_ITEM.IPA_WARNING'), AlertType.Error);
              }
            })
          );
          if ([this.salesPersonId, res.createdById, res.nationalAccountManagerId].includes(this.userId) || [TITLE.ADMIN, TITLE.SUPPORT].includes(this.title)) {
            this.submitAccess = false;
          } else {
            this.submitAccess = true;
          }
          this.subscriptionManager.add(
            this.ipaService.getQuoteSubject().subscribe(res => {
              if (res) {
                this.showTotalCost = res;
              }
            })
          );
          this.getSubject();
          this.setPageableObject(
            0
          );
          this.setViewPageableObject(
            1
          );
          this.pageableQuery.asc = AppConstants.sortByDescDirection;
          this.pageableQuery.orderBy = AppConstants.id;
          this.getIPAVersionListing();
          this.checkIfFieldsNeedsToBeDisabled();
        }
      })
    );
    this.subscriptionManager.add(
      this.ipaService.getDataSubject().subscribe(res => {
        this.versionID = res;
        this.getAllIntegration();
        this.getIPA();
      })
    );
    if (this.title === TITLE.IPA) {
      this.projectNumber.setValidators(Validators.required);
      this.designEngineer.setValidators(Validators.required);
    } else {
      this.projectNumber.clearValidators();
      this.designEngineer.clearValidators();
    }
    this.isOpenIPA = this.router.getCurrentNavigation()?.extras?.state?.isOpenIPA;
    this.projectNumber.updateValueAndValidity();
    this.designEngineer.updateValueAndValidity();
    this.tm.disable();
    this.rsm.disable();
    this.nam.disable();
    this.totalCost.disable();
    this.companyName.disable();
    this.getSubject();
    if (this.roles.includes(ROLE.ADMIN) && this.title === null) {
      this.isAdmin = true;
    } else {
      this.isAdmin = false;
    }
    this.cdf.detectChanges();
  }

  getSubject() {
    if (this.customIntegrationForm.invalid) {
      this.ipaService.setValidFormSubject(this.customIntegrationForm.invalid);
    }
  }

  revertIPA() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.REVERT_IPA.TITLE');
    const _description = this.translatePipe.transform('CAPEX_WORKFLOW.REVERT_IPA.BODY');
    this.isReverting = true;
    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        this.isReverting = false;
        return;
      }
      this.subscriptionManager.add(
        this.ipaService.revertIPA(this.userId, this.id).subscribe(() => {
          this.isReverting = false;
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('CAPEX_WORKFLOW.REVERT_IPA.SUCCESS'), AlertType.Success);
          this.ipaService.setDataSubject(this.ipaDTO.ipaVersionId);
          this.cdf.detectChanges();
        }, () => {
          this.isReverting = false;
          this.cdf.detectChanges();
        })
      );
    });
  }

  handleFormChange(event) {
    if (event.type === AppConstants.change) {
      this.ipaService.setSubmitStatusSubject(false);
      this.ipaService.setFormSubject(this.customIntegrationForm);
      this.cdf.detectChanges();
    }
  }

  setNoneOption(res) {
    if (!res.companyAddress.country) {
      this.companyCountry.setValue(null);
    }
    if (!res.nationalAccountManagerId) {
      this.nam.setValue(null);
    }
  }

  getAllIntegration() {
    this.subscriptionManager.add(
      this.ipaService.getIntegrations(this.versionID).subscribe((res: Integration[]) => {
        res.forEach(data => {
          if (data?.commentsAndQuote?.systemNumber !== null) {
            this.closeEnabled = false;
          } else {
            this.closeEnabled = true;
          }
        })
        if (res.length || this.projectNumber.value) {
          this.isIntegration = true;
          this.showTotalCost = true;
        } else {
          this.isIntegration = false;
          this.showTotalCost = false;
        }
      })
    );
  }

  getIPA() {
    this.subscriptionManager.add(
      this.ipaService.getIPA(this.versionID).subscribe((res: IPA) => {
        this.setDataToForm(res);
        if ([this.salesPersonId, res.createdById, res.nationalAccountManagerId].includes(this.userId) || [TITLE.ADMIN, TITLE.SUPPORT].includes(this.title)) {
          this.submitAccess = false;
        } else {
          this.submitAccess = true;
        }
        this.ipaStatus = res.ipaStatus;
        this.getSubject();
      })
    );
  }

  submitAndSave() {
    const _title = this.translatePipe.transform('CAPEX_WORKFLOW.SUBMIT_APPROVAL.TITLE');
    const _description = this.translatePipe.transform('IPA.SUBMIT_APPROVAL.BODY');
    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    if (this.customIntegrationForm.invalid) {
      this.customIntegrationForm.markAllAsTouched();
      return;
    }
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        this.isApproving = false;
        return;
      }
      this.isApproving = true;
      this.subscriptionManager.add(this.ipaService.createIPA(this.addData(), this.userId).subscribe((res: IPA) => {
        this.isSubmitting = false;
        this.ipaDTO = res;
        this.setDataToForm(res);
        this.ipaService.setIPASubject(res);
        this.ipaService.setValidFormSubject(false);
        this.subscriptionManager.add(
          this.ipaService.submitApproval(this.userId, this.capexId).subscribe(() => {
            this.isApproving = false;
            this.ipaService.setDataSubject(this.ipaDTO.ipaVersionId);
            this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.SUBMIT_APPROVAL.SUCCESS'), AlertType.Success);
            this.cdf.detectChanges();
          }, () => {
            this.isApproving = false;
            this.cdf.detectChanges();
          })
        );
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform(this.id ? 'IPA.CUSTOM_INTEGRATION.UPDATE_MSG' : 'IPA.CUSTOM_INTEGRATION.SUCCESS_MSG'), AlertType.Success);
        this.cdf.detectChanges();
      }, () => this.onError()));
    });
  }

  closeIPA() {
    const _title = this.translatePipe.transform('IPA_DASHBOARD.CLOSE_IPA_MODAL.TITLE');
    const _description = this.translatePipe.transform('IPA_DASHBOARD.CLOSE_IPA_MODAL.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(
        this.ipaService.closeIPA(this.userId, this.capexId).subscribe(() => {
          this.ipaService.setDataSubject(this.ipaDTO.ipaVersionId);
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA_DASHBOARD.CLOSE_IPA'), AlertType.Success);
          this.cdf.detectChanges();
        }, () => {
        })
      );
    })
  }

  cancelIPA() {
    const _title = this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA_MODAL.TITLE');
    const _description = this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA_MODAL.CANCEL_BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.isCancelling = true;
      this.subscriptionManager.add(
        this.ipaService.cancelIPA(this.userId, this.capexId).subscribe(() => {
          this.isCancelling = false;
          this.ipaService.setDataSubject(this.ipaDTO.ipaVersionId);
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA_DASHBOARD.CANCEL_IPA'), AlertType.Success);
          this.cdf.detectChanges();
        }, () => {
          this.isCancelling = false;
          this.cdf.detectChanges();
        })
      );
    })
  }

  getIPAVersionListing() {
    this.subscriptionManager.add(this.ipaService.getVersionList(this.id, this.pageableQuery).subscribe((res: VersionListPagedData) => {
      this.versionList = res.content;
      this.totalElements = res.totalElements;
      this.showCurrentPagination(res.pageable.pageNumber, res.pageable.pageSize, res.numberOfElements, res.totalElements);
      this.viewPageableObj.size = res.pageable.pageSize;
      this.viewPageableObj.page = res.pageable.pageNumber + 1;
      this.cdf.detectChanges();
    }, () => {
    }));
  }

  setPageableObject(
    page?: number,
    size?: number
  ) {
    this.pageableQuery.page = page;
    if (size) {
      this.pageableQuery.size = size;
    } else {
      this.pageableQuery.size = this.paginationConfig.pageSize;
    }
    this.pageableQuery.asc = this.pageableQuery.asc;
    this.pageableQuery.orderBy = this.pageableQuery.orderBy;
    this.pageableQuery.or = true;
  }

  setViewPageableObject(
    page?: number,
    size?: number
  ) {
    this.viewPageableObj.page = page;
    if (size) {
      this.viewPageableObj.size = size;
    } else {
      this.viewPageableObj.size = this.paginationConfig.pageSize;
    }
    this.viewPageableObj.asc = this.pageableQuery.asc;
    this.viewPageableObj.orderBy = this.viewPageableObj.orderBy;
  }

  getSorting() {
    this.setPageableObject(
      this.pageableQuery.page,
      this.pageableQuery.size
    );
    this.loading$.next(true);
    this.pageableQuery.orderBy;
    this.pageableQuery.asc = this.activeReverse;
    this.getIPAVersionListing();
  }

  getPagination(event, isPage: boolean) {
    this.setPageableObject(
      isPage ? event - 1 : this.pageableQuery.page,
      isPage ? this.pageableQuery.size : event
    );
    this.loading$.next(true);
    this.pageableQuery.orderBy;
    this.pageableQuery.asc;
    this.getIPAVersionListing();
  }

  showCurrentPagination(
    page?: number,
    size?: number,
    numberOfElements?: number,
    totalElements?: number
  ) {
    this.totalPaginationObj = new PageableQuery();
    this.totalPaginationObj.page = page;
    this.totalPaginationObj.size = size;
    if (numberOfElements) {
      this.totalPaginationObj.numberOfElements = numberOfElements;
    }
    if (totalElements) {
      this.totalPaginationObj.totalElements = totalElements;
    }
  }

  createNewVersion() {
    const _title = this.translatePipe.transform('IPA.VERSION_MODAL.TITLE');
    const _description = this.translatePipe.transform('IPA.VERSION_MODAL.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(
        this.ipaService.createNewVersion(this.id, this.userId).subscribe((res: IPA) => {
          this.ipaDTO = res;
          this.ipaService.setIPASubject(res);
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.VERSION'), AlertType.Success);
          this.cdf.detectChanges();
        }, () => {
          this.cdf.detectChanges();
        })
      );
    });
  }

  cloneIPA(id: number) {
    const _title = this.translatePipe.transform('IPA.CLONE_MODAL.TITLE');
    const _description = this.translatePipe.transform('IPA.CLONE_MODAL.BODY');

    const dialogRef = this.layoutUtilsService.submitElement(_title, _description);
    dialogRef.afterClosed().subscribe(res => {
      if (!res) {
        return;
      }
      this.subscriptionManager.add(
        this.ipaService.cloneIPA(id, this.userId).subscribe((res: IPA) => {
          this.ipaDTO = res;
          this.ipaService.setIPASubject(res);
          this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.CLONE'), AlertType.Success);
          this.cdf.detectChanges();
        }, () => {
          this.cdf.detectChanges();
        })
      );
    });
  }

  openSidebar(sidebarParams: SidebarParams<OverheadHopper>, template: MatSidenav): void {
    this.isOverheadHopperVisible = true;
    this.overheadHopperSidebarParams = sidebarParams;
    template.toggle();
  }

  sidebarClosed(isClosed: boolean, template: MatSidenav) {
    if (isClosed) {
      this.isOverheadHopperVisible = false;
      template.toggle();
    }
  }

  openUndertableSidebar(sidebarParams: SidebarParams<UndertableCart>, template: MatSidenav): void {
    this.isUndercartVisible = true;
    this.underTableCartSidebarParams = sidebarParams;
    template.toggle();
  }

  sidebarUndertableClosed(isClosed: boolean, template: MatSidenav) {
    if (isClosed) {
      this.isUndercartVisible = false;
      template.toggle();
    }
  }

  openSmartlineSidebar(sidebarParams: SidebarParams<Smartline>, template: MatSidenav): void {
    this.isSmartlineVisible = true;
    this.smartlineSidebarParams = sidebarParams;
    template.toggle();
  }

  sidebarSmartlineClosed(isClosed: boolean, template: MatSidenav) {
    if (isClosed) {
      this.isSmartlineVisible = false;
      template.toggle();
    }
  }

  openSAMSidebar(sidebarParams: SidebarParams<ConceptSAMS>, template: MatSidenav): void {
    this.isSAMVisible = true;
    this.SAMSidebarParams = sidebarParams;
    template.toggle();
  }

  sidebarSAMClosed(isClosed: boolean, template: MatSidenav) {
    if (isClosed) {
      this.isSAMVisible = false;
      template.toggle();
    }
  }

  getAllNationalAccount() {
    const titleId = TitleId.NAM;
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.userService.getManagerByTitle(titleId).subscribe((res: User[]) => {
        this.salesPersonByTitle = res;
        this.loading$.next(false);
        this.cdf.detectChanges();
      }, () => {
        this.loading$.next(false);
        this.cdf.detectChanges();
      })
    );
  }

  initializeForm(): FormGroup {
    return new FormGroup({
      tm: new FormControl('', [Validators.required]),
      requestDate: new FormControl('', Validators.required),
      nam: new FormControl(''),
      rsm: new FormControl('', [Validators.required]),
      fst: new FormControl('', [Validators.required]),
      companyName: new FormControl('', [Validators.required]),
      companyAddress: new FormControl(''),
      companyCity: new FormControl('', [Validators.required]),
      companyState: new FormControl('', [Validators.required]),
      companyZip: new FormControl(''),
      companyCountry: new FormControl(''),
      companyContact: new FormControl(''),
      companyPhone: new FormControl(''),
      projectNumber: new FormControl(''),
      designEngineer: new FormControl(''),
      totalCost: new FormControl('')
    })
  }

  setDataToForm(ipaContent: IPA) {
    this.customIntegrationForm.setValue({
      tm: ipaContent.territoryManagerName,
      requestDate: ipaContent.requestDate,
      nam: ipaContent.nationalAccountManagerName,
      rsm: ipaContent.regionalSalesManagerName,
      fst: ipaContent.fst,
      companyName: ipaContent.companyAddress.name,
      companyAddress: ipaContent.companyAddress.streetAddress,
      companyCity: ipaContent.companyAddress.city,
      companyState: ipaContent.companyAddress.state,
      companyZip: ipaContent.companyAddress.zipCode,
      companyCountry: ipaContent.companyAddress.country,
      companyContact: ipaContent.companyAddress.contactName,
      companyPhone: ipaContent.companyAddress.phone,
      projectNumber: ipaContent.projectNumber,
      designEngineer: ipaContent.designEngineer,
      totalCost: ipaContent.totalCost
    });
  }

  addData(): IPA {
    const ipaDTO = new IPA();
    ipaDTO.capexAirPlusPaperPlusId = this.capexId;
    ipaDTO.companyAddress.id = this.companyAddressId ? this.companyAddressId : null;
    ipaDTO.companyAddress.streetAddress = this.companyAddress.value;
    ipaDTO.companyAddress.city = this.companyCity.value;
    ipaDTO.companyAddress.name = this.companyName.value;
    ipaDTO.companyAddress.state = this.companyState.value;
    ipaDTO.companyAddress.country = this.companyCountry.value ? this.companyCountry.value : null;
    ipaDTO.companyAddress.zipCode = this.companyZip.value;
    ipaDTO.companyAddress.contactName = this.companyContact.value;
    ipaDTO.companyAddress.phone = this.companyPhone.value;
    ipaDTO.requestDate = this.requestDate.value;
    ipaDTO.ipaVersionId = this.versionID;
    ipaDTO.id = this.id ? this.id : null;
    ipaDTO.fst = this.fst.value;
    ipaDTO.nationalAccountManagerId = this.namId ?? null;
    ipaDTO.territoryManagerId = this.tmId;
    ipaDTO.regionalSalesManagerId = this.rsmId;
    ipaDTO.createdById = this.ipaDTO.createdById ? this.ipaDTO.createdById : this.userId;
    ipaDTO.createdOn = this.currentDateUTC();
    ipaDTO.ipaStatus = this.ipaStatus;
    ipaDTO.projectNumber = this.projectNumber.value ? this.projectNumber.value : null;
    ipaDTO.designEngineer = this.designEngineer.value ? this.designEngineer.value : null;
    ipaDTO.totalCost = this.totalCost.value ? this.totalCost.value : null;
    return ipaDTO;
  }

  submit() {
    const controls = this.customIntegrationForm.controls;
    if (this.customIntegrationForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      const invalidField = [];
      for (const name in controls) {
        if (controls[name].invalid) {
          invalidField.push(name);
          if (invalidField && invalidField.length) {
            this.layoutUtilsService.showActionNotification(
              this.translatePipe.transform('COMMON.ERROR_FIELDS') + Array.prototype.map.call(invalidField, (field) => field).join(", ") + '\n' + this.translatePipe.transform('COMMON.ERROR_DETAIL'), AlertType.Error);
          }
        }
      }
      return;
    }
    this.isSubmitting = true;
    this.subscriptionManager.add(this.ipaService.createIPA(this.addData(), this.userId).subscribe((res: IPA) => {
      this.isSubmitting = false;
      this.ipaDTO = res;
      this.setDataToForm(res);
      this.ipaService.setIPASubject(res);
      this.ipaService.setValidFormSubject(false);
      if (this.id) {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.CUSTOM_INTEGRATION.UPDATE_MSG'), AlertType.Success);
      } else {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.CUSTOM_INTEGRATION.SUCCESS_MSG'), AlertType.Success);
      }
      this.ipaService.setSubmitStatusSubject(true);
      this.cdf.detectChanges();
    }, () => this.onError()));
  }

  onError() {
    this.isSubmitting = false;
    this.cdf.detectChanges();
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.customIntegrationForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }

  getFile(url: string, filename: string) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'arraybuffer';
    xhr.onload = function () {
      if (this.status === 200) {
        saveAs(new File([this.response], filename));
      }
    };
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
    xhr.send();
  }

  downloadFile(docPath: IPAVersion) {
    this.getFile(docPath.url, docPath.fileName);
    this.layoutUtilsService.showActionNotification(this.translatePipe.transform('IPA.DOWNLOAD_SUCCESS'));
  }

}
