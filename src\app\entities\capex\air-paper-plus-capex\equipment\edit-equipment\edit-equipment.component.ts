import { Component, OnInit, Output, Input, EventEmitter, ChangeDetectorRef, SimpleChanges } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { FormGroup, FormControl, Validators, FormGroupDirective } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { MaterialCategory } from '@shared/models/materialCategory.model';
import { MasterDataService } from '@entities/master-data-management/master-data.service';
import { ENTITY, FILTER_OPERATOR, AppConstants, CATEGORY_ID } from '@shared/constants';
import { FilterObject, SortField, Filter } from '@shared/models/filter.model';
import { Equipment, EquipmentInfo } from '@shared/models/equipment.model';
import { PageableQuery } from '@shared/services/utils';
import { AirPaperPlusCapexService } from '../../air-paper-plus-capex.service';
import { Material } from '@shared/models/material.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';

@Component({
  selector: 'sfl-edit-equipment',
  templateUrl: './edit-equipment.component.html',
  styleUrls: ['./edit-equipment.component.scss']
})
export class EditEquipmentComponent extends SflBaseComponent implements OnInit {
  @Output() closeSidebarEvent: EventEmitter<boolean> = new EventEmitter();
  @Input() sidebarParams: SidebarParams<any>;
  @Input() capexId: number;
  @Input() disableCAPEX: boolean;
  @Input() currencyId: number;
  @Input() isFastLane: boolean;
  equipmentTitle: string;
  isAddMore: boolean;
  equipment: EquipmentInfo = new EquipmentInfo();
  equipmentForm: FormGroup = this.initializeForm();
  numberPattern = AppConstants.numberPattern;
  decimalNumberPatternWithZero = AppConstants.decimalNumberPatternWithZero;
  machineNumber = this.equipmentForm.controls['machineNumber'];
  machineType = this.equipmentForm.controls['machineType'];
  description = this.equipmentForm.controls['description'];
  monthlyRent = this.equipmentForm.controls['monthlyRent'];
  materialUsed = this.equipmentForm.controls['materialUsed'];
  quantity = this.equipmentForm.controls['quantity'];
  lease = this.equipmentForm.controls['lease'];
  machineCostPerMonth = this.equipmentForm.controls['machineCostPerMonth'];
  costToTerritory = this.equipmentForm.controls['costToTerritory'];
  materialRevenue = this.equipmentForm.controls['materialRevenue'];
  materialMargin = this.equipmentForm.controls['materialMargin'];
  allListedMaterials = this.equipmentForm.controls['allListedMaterials'];
  equipmentId: number;
  equipmentMachineCategoryIds = null;
  equipmentType = null;
  machineCategories: MaterialCategory[];
  pageableQuery: PageableQuery = new PageableQuery();
  filter: FilterObject = new FilterObject();
  equipmentList: Equipment[] = [];
  equipmentData: Equipment[] = [];
  descEquipmentData: Equipment[] = [];
  number: string;
  materialDesc: string;
  materialInfo: Material[] = [];
  materialUsedData: Material[] = [];
  masterEquipmentId: number;
  sorting: SortField = new SortField();
  numberSearchField = new FormControl('');
  descSearchField = new FormControl('');
  materialNumberSearchField = new FormControl('');
  @Input() materialCategory: MaterialCategory[];
  isDifferentGroup: boolean;

  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly translatePipe: TranslatePipe,
    private readonly materialService: MasterDataService,
    private readonly masterDataService: MasterDataService,
    private readonly capexService: AirPaperPlusCapexService,
    private readonly layoutUtilsService: LayoutUtilsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.equipmentMachineCategoryIds = this.appConstants.AP_PP_CATEGORY_IDS;
    this.subscriptionManager.add(
      this.numberSearchField.valueChanges.subscribe(() => {
        this.filterNumber();
      })
    );

    this.subscriptionManager.add(
      this.descSearchField.valueChanges.subscribe(() => {
        this.filterDescription();
      })
    );

    this.subscriptionManager.add(
      this.materialNumberSearchField.valueChanges.subscribe(() => {
        this.filterMaterialNumber();
      })
    );
  }

  filterNumber() {
    this.equipmentData = [];
    this.equipmentData = this.equipmentList.filter(data => {
      if (this.numberSearchField.value) {
        if(data.number.toString().includes(this.numberSearchField.value)) {
          this.equipmentData.push(data);
          return this.equipmentData;
        }
      } else {
        return this.equipmentList;
      }
    });
  }

  filterMaterialNumber() {
    this.materialUsedData = [];
    this.materialUsedData = this.materialInfo.filter(data => {
      if (this.materialNumberSearchField.value) {
        if(data.number.toString().includes(this.materialNumberSearchField.value)) {
          this.materialUsedData.push(data);
          return this.materialUsedData;
        }
      } else {
        return this.materialInfo;
      }
    });
  }

  filterDescription() {
    this.descEquipmentData = [];
    this.descEquipmentData = this.equipmentList.filter(data => {
      if (this.descSearchField.value) {
        if(data.description.toLowerCase().includes(this.descSearchField.value) || data.description.includes(this.descSearchField.value)) {
          this.descEquipmentData.push(data);
          return this.descEquipmentData;
        }
      } else {
        return this.equipmentList;
      }
    });
  }

  getEquipmentDetails() {
    if (this.equipmentId) {
      this.subscriptionManager.add(
        this.capexService.getEquipmentInfo(this.equipmentId).subscribe((res: EquipmentInfo) => {
          this.equipment = res;
          this.setData(this.equipment);
          this.getAllEquipmentListing(this.equipment.materialCategoryId);
          if(this.equipment.allListedMaterials) {
            this.materialUsed.disable();
            this.materialUsed.clearValidators();
          } else {
            this.materialUsed.enable();
            this.materialUsed.setValidators(Validators.required);
          }
          this.materialUsed.updateValueAndValidity();
          this.loading$.next(false);
          this.cdf.detectChanges();
        }, () => this.loading$.next(false))
      );
    } else {
      this.loading$.next(false);
    }
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
    this.setComponentTitle();
    if (simpleChanges.sidebarParams && simpleChanges.sidebarParams.currentValue) {
      this.getAllMachineCategories();
      if (this.sidebarParams.isEdit) {
        this.equipmentId = this.sidebarParams.data.id;
        if(!this.allListedMaterials.value) {
          this.getAllMaterialListing(this.capexId, this.sidebarParams.data?.equipmentId);
        }
      }
      this.getEquipmentDetails();
    }
    this.lease.disable();
    this.machineCostPerMonth.disable();
    this.costToTerritory.disable();
    this.materialMargin.disable();
    this.materialRevenue.disable();
    if(!this.allListedMaterials.value) {
      this.materialUsed.setValidators(Validators.required);
    }
    if(this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  getAllMaterialListing(capexId: number, equipmentId: number) {
    this.loading$.next(true);
    this.subscriptionManager.add(this.capexService.getCapexMaterialListing(capexId, equipmentId).subscribe((material: Material[]) => {
      this.loading$.next(false);
      this.materialInfo = material;
      if(this.equipment.materialCategoryId === CATEGORY_ID.PAPERPLUS) {
        if(material.length) {
          this.isDifferentGroup = false;
        } else {
          this.isDifferentGroup = true;
        }
      }
      this.materialUsedData = material;
      this.cdf.detectChanges();
    }, () => {
      this.loading$.next(false);
    }));
  }

  handleMaterialListed(event) {
    if(event.checked) {
      this.materialUsed.disable();
      this.materialUsed.setValue('');
      this.materialUsed.clearValidators();
    } else {
      this.materialUsed.enable();
      this.materialUsed.setValidators(Validators.required);
      if(this.machineNumber.value || this.description.value) {
        this.getAllMaterialListing(this.capexId, this.equipment.equipmentId);
      }
    }
    this.materialUsed.updateValueAndValidity();
    if(this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  initializeForm() {
    return new FormGroup({
      machineNumber: new FormControl('', [Validators.required]),
      machineType: new FormControl('', [Validators.required]),
      description: new FormControl('', [Validators.required]),
      monthlyRent: new FormControl('', [Validators.required]),
      materialUsed: new FormControl(''),
      quantity: new FormControl('', [Validators.required]),
      machineCostPerMonth: new FormControl(''),
      lease: new FormControl(''),
      costToTerritory: new FormControl(''),
      materialRevenue: new FormControl(''),
      materialMargin: new FormControl(''),
      allListedMaterials: new FormControl(false)
    });
  }

  clearNotClonableFields() {
    this.equipmentForm.reset();
  }

  addData(): EquipmentInfo {
    const equipment = new EquipmentInfo();
    equipment.allListedMaterials = this.allListedMaterials.value;
    equipment.capexAirPlusPaperPlusId = this.capexId;
    equipment.materialCategoryId = this.machineType.value;
    equipment.number = this.number ? this.number : this.machineNumber.value;
    equipment.description = this.materialDesc ? this.materialDesc : this.description.value;
    equipment.quantity = +this.quantity.value;
    equipment.rentChargeEach = +this.monthlyRent.value;
    equipment.airPlusPaperPlusMaterialId = this.materialUsed.value ? this.materialUsed.value : null;
    equipment.leaseRevenuePerMonth = this.lease.value ? +this.lease.value : 0;
    equipment.machineCostPerMonth = this.machineCostPerMonth.value ? +this.machineCostPerMonth.value : 0;
    equipment.monthlyCostToTerritory = this.costToTerritory.value ? +this.costToTerritory.value : 0;
    equipment.revenuePerMachinePer100Feet = this.materialRevenue.value ? +this.materialRevenue.value : 0;
    equipment.marginPerMachinePer100Feet = this.materialMargin.value ? +this.materialMargin.value : 0;
    equipment.id = this.equipmentId ? this.equipmentId : null;
    equipment.equipmentId = this.equipment.equipmentId;
    equipment.monthlyRent = this.equipment.monthlyRent ? this.equipment.monthlyRent : 0;
    return equipment;
  }

  setData(equipment: EquipmentInfo) {
    this.equipmentForm.setValue({
      allListedMaterials: equipment.allListedMaterials,
      machineType: equipment.materialCategoryId,
      machineNumber: equipment.number,
      description: equipment.description,
      quantity: equipment.quantity,
      monthlyRent: equipment.rentChargeEach,
      materialUsed: equipment.airPlusPaperPlusMaterialId ? equipment.airPlusPaperPlusMaterialId : null,
      lease: equipment.leaseRevenuePerMonth,
      machineCostPerMonth: equipment.machineCostPerMonth,
      costToTerritory: equipment.monthlyCostToTerritory,
      materialRevenue: equipment.revenuePerMachinePer100Feet,
      materialMargin: equipment.marginPerMachinePer100Feet,
    });
  }

  handleMaterialCategory(event) {
    if (event.value) {
      this.getAllEquipmentListing(event.value);
    }
    if(this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  calculateValue() {
    if(this.equipmentForm.valid && this.machineType.value) {
      this.loading$.next(true);
      this.subscriptionManager.add(
        this.capexService.calculateEquipmentValue(this.addData()).subscribe((res: EquipmentInfo) => {
          this.loading$.next(false);
          this.lease.setValue(res.leaseRevenuePerMonth);
          this.machineCostPerMonth.setValue(res.machineCostPerMonth);
          this.costToTerritory.setValue(res.monthlyCostToTerritory);
          this.materialRevenue.setValue(res.revenuePerMachinePer100Feet);
          this.materialMargin.setValue(res.marginPerMachinePer100Feet);
        }, () => {
          this.loading$.next(false);
        })
      );
    }
  }

  handleCalculation(event) {
    if(this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  onSubmit() {
    const controls = this.equipmentForm.controls;
    if (this.equipmentForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      return;
    }
    this.isSubmitting = true;
    this.subscriptionManager.add(this.capexService.addEquipmentInfo(this.addData()).subscribe(res => {
      this.isSubmitting = false;
      this.equipment = res;
      if (this.equipmentId) {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('EQUIPMENT.UPDATE_SUCCESS'), AlertType.Success);
      } else {
        this.layoutUtilsService.showActionNotification(this.translatePipe.transform('EQUIPMENT.CREATE_SUCCESS'), AlertType.Success);
      }
      this.capexService.setCapexId(this.equipment.capexAirPlusPaperPlusId);
      this.capexService.setAuditIdSubject(this.equipment.capexAirPlusPaperPlusId);
      this.capexService.setEditFormStatus(true);
      if (!this.isAddMore) {
        this.closeSidebar();
      }
      this.clearNotClonableFields();
      this.cdf.detectChanges();
    }, () => this.onError()));
  }

  onError() {
    this.isSubmitting = false;
    this.cdf.detectChanges();
  }

  resetForm(form: FormGroupDirective) {
    form.resetForm();
    this.setComponentTitle();
  }

  setComponentTitle() {
    this.equipmentTitle = this.sidebarParams.isEdit && !this.sidebarParams.isClone ? this.translatePipe.transform('EQUIPMENT.EDIT_TITLE') : this.translatePipe.transform('AP_PP_CAPEX.EQUIPMENT.ADD_TITLE');
  }

  closeSidebar() {
    this.closeSidebarEvent.next(true);
  }

  handleNumber(event) {
    this.materialUsed.setValue('');
    if(event.value) {
      const material = this.equipmentList.find((data) => {
        if(data.number === event.value) {
          return data.number === event.value;
        }
      });
      this.description.setValue(material.description);
      if(!this.allListedMaterials.value) {
        this.getAllMaterialListing(this.capexId, material.id);
      }
      this.number = material.number.toString();
      this.equipment.equipmentId = material.id;
      this.equipment.monthlyRent = material.monthlyRent;
      this.equipment.materialCategoryId = material.materialCategoryId;
    }
    if(!this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  handleDesc(event) {
    this.materialUsed.setValue('');
    if(event.value) {
      const material = this.equipmentList.find((data) => {
        if(data.description === event.value) {
          return data.description === event.value;
        }
      });
      if(!this.allListedMaterials.value) {
        this.getAllMaterialListing(this.capexId, material.id);
      }
      this.machineNumber.setValue(material.number);
      this.materialDesc = material.description;
      this.equipment.equipmentId = material.id;
      this.equipment.monthlyRent = material.monthlyRent;
      this.equipment.materialCategoryId = material.materialCategoryId;
    }
    if(this.equipmentForm.valid) {
      this.calculateValue();
    }
  }

  getAllMachineCategories() {
    this.subscriptionManager.add(
      this.masterDataService.getAllMaterialCategories().subscribe((res: MaterialCategory[]) => {
        this.machineCategories = this.masterDataService.getMaterialCategoriesByCategoryIds(res, this.equipmentMachineCategoryIds)
        this.cdf.detectChanges();
      }, () => { })
    );
  }

  getAllEquipmentListing(categoryId: number) {
    this.setFilter(categoryId);
    this.loading$.next(true);
    this.subscriptionManager.add(this.materialService.getDataByFilterCurrency(this.filter, this.currencyId).subscribe((res: Equipment[]) => {
      this.loading$.next(false);
      this.equipmentList = JSON.parse(JSON.stringify(res));
      this.equipmentData = JSON.parse(JSON.stringify(res));
      this.descEquipmentData = res;
      this.sortDescription(this.descEquipmentData);
      this.filter = new FilterObject();
      this.cdf.detectChanges();
    }, () => {
      this.loading$.next(false);
      this.filter = new FilterObject();
      this.cdf.detectChanges();
    }));
  }

  setFilter(categoryId: number) {
    this.filter.entity = ENTITY.EQUIPMENT;
    this.filter.operator = FILTER_OPERATOR.AND;
    this.filter.left = null;
    this.filter.right = null;
    const materialFilter: Filter = new Filter();
    materialFilter.dataType = AppConstants.longDataType;
    materialFilter.key = 'materialCategory.id';
    materialFilter.operator = AppConstants.eq;
    materialFilter.value = categoryId.toString();
    const isActive: Filter = new Filter();
    isActive.dataType = AppConstants.booleanDataType;
    isActive.key = 'active';
    isActive.operator = AppConstants.eq;
    isActive.value = 'true';
    if(!this.isFastLane) {
      const status: Filter = new Filter();
      status.dataType = AppConstants.enumDataType;
      status.enumName = AppConstants.status;
      status.operator = AppConstants.eq;
      status.key = 'status';
      status.value = AppConstants.live;
      this.filter.value.push(status);
    }
    this.filter.value.push(isActive);
    this.filter.value.push(materialFilter);
    this.sorting.ascending = AppConstants.ascDirection;
    this.sorting.field = AppConstants.numberField;
    this.filter.orderBy.push(this.sorting);
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.equipmentForm.controls[controlName];
    if (!control) {
      return false;
    }

    return control.hasError(validationType) && (control.dirty || control.touched);
  }

}
