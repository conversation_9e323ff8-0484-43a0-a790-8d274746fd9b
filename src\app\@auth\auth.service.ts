/**
 * @sunflowerlab
 * <AUTHOR>
 */
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiUrl, AppConstants } from "../@shared/constants";
import { HttpClient } from "@angular/common/http";
import { map } from "rxjs/operators";
import { User, ResetPasswordModel, ChangePasswordModel } from "../@shared/models/user.model";
import { Router } from "@angular/router";

@Injectable({
  providedIn: "root",
})
export class AuthService {
  constructor(
    private readonly http: HttpClient,
    private readonly router: Router
  ) {}

  login(credentials): Observable<string> {
    const data = {
      username: credentials.username,
      password: credentials.password,
    };
    return this.http
      .post(ApiUrl.login, data)
      .pipe(map(authenticateSuccess.bind(this)));

    function authenticateSuccess(resp) {
      const bearerToken = resp.id_token;
      if (bearerToken && window.localStorage) {
        localStorage.setItem(AppConstants.authenticationToken, bearerToken);
        return bearerToken;
      }
    }
  }

  register(registerUser): Observable<string> {
    return this.http.post<string>(ApiUrl.register, registerUser);
  }

  getAccount(): Observable<User> {
    return this.http.get<User>(ApiUrl.account);
  }

  forgetPasswordInit(email: string): Observable<void> {
    return this.http.post<void>(ApiUrl.forgetPassword.init, { email });
  }

  forgetPasswordFinish(resetPassword: ResetPasswordModel): Observable<void> {
    return this.http.post<void>(ApiUrl.forgetPassword.finish, { 
      newPassword: resetPassword.newPassword,
      key: resetPassword.key
     });
  }

  changePassword(passwordModel: ChangePasswordModel) {
    return this.http.post(ApiUrl.changePassword, passwordModel);
  }
}
