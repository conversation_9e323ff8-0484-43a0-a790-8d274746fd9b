export class Filter {
  dataType: string = 'STRING';
  key: string;
  operator: string = 'LI';
  value: string;
  enumName?: string;
}

export class FilterObject {
  entity: string;
  operator: string;
  value: Filter[] = [];
  left: Node = new Node();
  right: Node = new Node();
  orderBy: SortField[] = [];
}

export class Node {
  entity?: string;
  operator?: string;
  value: Filter[] = [];
}

export class SortField {
  ascending: boolean;
  field: string;
}

export enum TitleId {
  GLOBAL = 0,
  VP = 1,
  NAM = 2,
  NSM = 3,
  RSM = 4,
  TM = 5,
  FPS = 6,
  ISS = 7,
  CS = 8,
  ADMIN = 100,
  SUPPORT = 101,
  ISS_GLOBAL = 102,
  ISS_NATIONAL = 103
}

export enum Category {
  AP = 1,
  PP = 2,
  FP = 3
}

export enum Property {
  AP_STANDARD = 1,
  PP_STANDARD = 6
}

export enum RegionId {
  OFFICE = 'Office',
  OFFICE_ID = 1,
  FOAMPLUS = 'FOAMplus',
  FOAMPLUS_ID = 9,
  CANADA_ID = 2,
  GLOBAL_ID = 0
}

export class ImportDTO {
  errors: Error[] = [];
  numberOfRowsInserted: number;
  numberOfRowsUpdated: number;
  differenceInHeadersException: ErrorHeader = new ErrorHeader();
}

export class ErrorHeader {
  requiredHeaders: string;
  fileHeaders: string;
  missingHeaders: string;
  extraHeaders: string;
}

export class Error {
  columnName: string;
  errorCode: number;
  errorMessage: string;
  lineNumber: number;
}
